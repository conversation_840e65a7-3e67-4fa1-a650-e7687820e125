# AgentsUI 后端API设计文档

## 1. API概述

### 1.1 技术栈
- **FastAPI**: 高性能Web框架
- **SQLAlchemy**: ORM框架
- **Pydantic**: 数据验证和序列化
- **Socket.IO**: WebSocket通信
- **JWT**: 身份认证
- **Redis**: 缓存和会话存储

### 1.2 API设计原则
- RESTful设计风格
- 统一的响应格式
- 完善的错误处理
- API版本控制
- 请求限流和安全防护

### 1.3 基础URL
```
开发环境: http://localhost:8000/api/v1
生产环境: https://api.agentsui.com/api/v1
WebSocket: ws://localhost:8000/ws
```

## 2. 通用响应格式

### 2.1 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 2.2 错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "email",
      "reason": "邮箱格式不正确"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 2.3 分页响应
```json
{
  "success": true,
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

## 3. 认证授权API

### 3.1 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "user123",
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "张三"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user_id": "uuid-string",
    "username": "user123",
    "email": "<EMAIL>",
    "full_name": "张三",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3.2 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "user123",
  "password": "password123"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "access_token": "jwt-token-string",
    "refresh_token": "refresh-token-string",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
      "user_id": "uuid-string",
      "username": "user123",
      "email": "<EMAIL>",
      "full_name": "张三"
    }
  }
}
```

### 3.3 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

### 3.4 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer <access_token>
```

## 4. Agent管理API

### 4.1 发现可用Agents
```http
GET /api/v1/agents/discover
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": [
    {
      "agent_id": "culture_agent",
      "name": "企业文化精神符号总结Agent",
      "description": "帮助分析企业文化并生成精神符号",
      "version": "1.0.0",
      "author": "AgentsUI Team",
      "tags": ["文化分析", "符号生成"],
      "config_schema": {
        "type": "object",
        "properties": {
          "openai_api_key": {"type": "string"},
          "model_name": {"type": "string", "default": "deepseek-chat"}
        }
      },
      "is_loaded": false,
      "status": "available"
    }
  ]
}
```

### 4.2 加载Agent
```http
POST /api/v1/agents/{agent_id}/load
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "config": {
    "openai_api_key": "sk-xxx",
    "model_name": "deepseek-chat"
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "agent_id": "culture_agent",
    "status": "loaded",
    "instance_id": "uuid-string",
    "loaded_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4.3 启动Agent
```http
POST /api/v1/agents/{agent_id}/start
Authorization: Bearer <access_token>
```

### 4.4 停止Agent
```http
POST /api/v1/agents/{agent_id}/stop
Authorization: Bearer <access_token>
```

### 4.5 卸载Agent
```http
POST /api/v1/agents/{agent_id}/unload
Authorization: Bearer <access_token>
```

### 4.6 获取Agent状态
```http
GET /api/v1/agents/{agent_id}/status
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "agent_id": "culture_agent",
    "status": "running",
    "instance_id": "uuid-string",
    "health": "healthy",
    "uptime": 3600,
    "memory_usage": "128MB",
    "cpu_usage": "5%",
    "last_activity": "2024-01-01T00:00:00Z"
  }
}
```

### 4.7 获取已加载的Agents
```http
GET /api/v1/agents/loaded
Authorization: Bearer <access_token>
```

## 5. 对话管理API

### 5.1 创建对话会话
```http
POST /api/v1/conversations
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "agent_id": "culture_agent",
  "title": "企业文化分析会话"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "conversation_id": "uuid-string",
    "agent_id": "culture_agent",
    "title": "企业文化分析会话",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5.2 获取对话列表
```http
GET /api/v1/conversations?page=1&size=20&agent_id=culture_agent
Authorization: Bearer <access_token>
```

### 5.3 获取对话详情
```http
GET /api/v1/conversations/{conversation_id}
Authorization: Bearer <access_token>
```

### 5.4 获取对话消息
```http
GET /api/v1/conversations/{conversation_id}/messages?page=1&size=50
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "message_id": "uuid-string",
        "conversation_id": "uuid-string",
        "sender": "user",
        "content": "请分析腾讯公司的企业文化",
        "message_type": "text",
        "timestamp": "2024-01-01T00:00:00Z"
      },
      {
        "message_id": "uuid-string",
        "conversation_id": "uuid-string",
        "sender": "agent",
        "content": "我将帮您分析腾讯公司的企业文化...",
        "message_type": "text",
        "data": {
          "step": "input_company",
          "waiting_for_user": true
        },
        "timestamp": "2024-01-01T00:00:01Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 50,
      "total": 2,
      "pages": 1
    }
  }
}
```

### 5.5 删除对话
```http
DELETE /api/v1/conversations/{conversation_id}
Authorization: Bearer <access_token>
```

## 6. 知识库管理API

### 6.1 获取知识库列表
```http
GET /api/v1/knowledge-bases?page=1&size=20
Authorization: Bearer <access_token>
```

### 6.2 创建知识库
```http
POST /api/v1/knowledge-bases
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "企业文化知识库",
  "description": "收集各种企业文化相关资料",
  "category": "business"
}
```

### 6.3 上传文档
```http
POST /api/v1/knowledge-bases/{kb_id}/documents
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

file: <binary_file>
metadata: {
  "title": "文档标题",
  "tags": ["tag1", "tag2"]
}
```

### 6.4 搜索文档
```http
GET /api/v1/knowledge-bases/{kb_id}/search?q=企业文化&limit=10
Authorization: Bearer <access_token>
```

## 7. WebSocket API

### 7.1 连接建立
```javascript
// 客户端连接
const socket = io('ws://localhost:8000/ws', {
  auth: {
    token: 'jwt-access-token'
  },
  query: {
    agent_id: 'culture_agent',
    conversation_id: 'uuid-string'
  }
});
```

### 7.2 消息格式

#### 用户消息
```json
{
  "type": "user_message",
  "data": {
    "content": "请分析腾讯公司",
    "message_type": "text",
    "conversation_id": "uuid-string"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Agent响应
```json
{
  "type": "agent_response",
  "data": {
    "content": "我将帮您分析腾讯公司的企业文化",
    "message_type": "text",
    "step": "input_company",
    "waiting_for_user": true,
    "conversation_id": "uuid-string"
  },
  "timestamp": "2024-01-01T00:00:01Z"
}
```

#### 系统消息
```json
{
  "type": "system_message",
  "data": {
    "event": "agent_status_changed",
    "agent_id": "culture_agent",
    "status": "running"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 7.3 事件类型

#### 客户端发送事件
- `user_message`: 用户发送消息
- `user_input`: 用户输入（如确认、回答问题）
- `ping`: 心跳检测

#### 服务端发送事件
- `agent_response`: Agent响应
- `agent_status`: Agent状态变化
- `error`: 错误消息
- `pong`: 心跳响应

## 8. 模型管理API

### 8.1 获取可用模型列表
```http
GET /api/v1/models
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": [
    {
      "model_id": "uuid-string",
      "model_name": "deepseek",
      "display_name": "DeepSeek Chat",
      "provider": "deepseek",
      "model_type": "chat",
      "description": "DeepSeek对话模型，支持代码生成和推理",
      "max_tokens": 4096,
      "supports_streaming": true,
      "supports_functions": false,
      "cost_per_1k_input": 0.001,
      "cost_per_1k_output": 0.002,
      "is_active": true,
      "has_api_key": true
    },
    {
      "model_id": "uuid-string",
      "model_name": "kimi-k2",
      "display_name": "Kimi K2",
      "provider": "kimi",
      "model_type": "chat",
      "description": "Kimi长文本模型，支持多模态交互",
      "max_tokens": 128000,
      "supports_streaming": true,
      "supports_functions": true,
      "cost_per_1k_input": 0.0015,
      "cost_per_1k_output": 0.003,
      "is_active": true,
      "has_api_key": true
    }
  ]
}
```

### 8.2 添加新模型
```http
POST /api/v1/models
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "model_name": "gpt-4",
  "display_name": "GPT-4",
  "provider": "openai",
  "model_type": "chat",
  "description": "OpenAI GPT-4模型",
  "api_endpoint": "https://api.openai.com/v1",
  "max_tokens": 8192,
  "supports_streaming": true,
  "supports_functions": true,
  "cost_per_1k_input": 0.03,
  "cost_per_1k_output": 0.06
}
```

### 8.3 更新模型配置
```http
PUT /api/v1/models/{model_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "display_name": "DeepSeek Chat Pro",
  "description": "升级版DeepSeek对话模型",
  "max_tokens": 8192,
  "is_active": true
}
```

### 8.4 删除模型
```http
DELETE /api/v1/models/{model_id}
Authorization: Bearer <access_token>
```

### 8.5 管理模型API密钥

#### 8.5.1 添加API密钥
```http
POST /api/v1/models/{model_id}/api-keys
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "key_name": "Production Key",
  "api_key": "sk-your-api-key-here",
  "expires_at": "2024-12-31T23:59:59Z"
}
```

#### 8.5.2 获取API密钥列表
```http
GET /api/v1/models/{model_id}/api-keys
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": [
    {
      "key_id": "uuid-string",
      "key_name": "Production Key",
      "api_key_masked": "sk-****...****a7",
      "is_active": true,
      "expires_at": "2024-12-31T23:59:59Z",
      "last_used_at": "2024-01-01T12:00:00Z",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### 8.5.3 删除API密钥
```http
DELETE /api/v1/models/{model_id}/api-keys/{key_id}
Authorization: Bearer <access_token>
```

### 8.6 模型配置管理

#### 8.6.1 创建模型配置
```http
POST /api/v1/models/{model_id}/configs
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "config_name": "Creative Writing",
  "temperature": 0.9,
  "max_tokens": 2000,
  "top_p": 0.95,
  "frequency_penalty": 0.1,
  "presence_penalty": 0.1,
  "stop_sequences": ["\n\n", "###"],
  "custom_params": {
    "system_prompt": "You are a creative writing assistant."
  }
}
```

#### 8.6.2 获取模型配置列表
```http
GET /api/v1/models/{model_id}/configs
Authorization: Bearer <access_token>
```

### 8.7 Agent模型绑定

#### 8.7.1 为Agent绑定模型
```http
POST /api/v1/agents/{agent_id}/models
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "model_id": "uuid-string",
  "config_id": "uuid-string",
  "is_primary": true,
  "priority": 1
}
```

#### 8.7.2 获取Agent绑定的模型
```http
GET /api/v1/agents/{agent_id}/models
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": [
    {
      "agent_model_id": "uuid-string",
      "model": {
        "model_id": "uuid-string",
        "model_name": "deepseek",
        "display_name": "DeepSeek Chat",
        "provider": "deepseek"
      },
      "config": {
        "config_id": "uuid-string",
        "config_name": "Default",
        "temperature": 0.7,
        "max_tokens": 1000
      },
      "is_primary": true,
      "priority": 1,
      "is_active": true
    }
  ]
}
```

### 8.8 模型测试

#### 8.8.1 测试模型连接
```http
POST /api/v1/models/{model_id}/test
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "test_message": "Hello, this is a test message.",
  "config_id": "uuid-string"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "response": "Hello! I received your test message successfully.",
    "response_time_ms": 1250,
    "tokens_used": {
      "input": 8,
      "output": 12,
      "total": 20
    },
    "cost": 0.00004
  }
}
```

### 8.9 模型使用统计

#### 8.9.1 获取模型使用统计
```http
GET /api/v1/models/{model_id}/usage?start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "total_requests": 1500,
    "total_tokens": 150000,
    "total_cost": 15.50,
    "average_response_time": 1200,
    "success_rate": 0.98,
    "daily_stats": [
      {
        "date": "2024-01-01",
        "requests": 50,
        "tokens": 5000,
        "cost": 0.52,
        "avg_response_time": 1100
      }
    ]
  }
}
```

#### 8.9.2 获取用户模型使用统计
```http
GET /api/v1/users/{user_id}/model-usage?period=month
Authorization: Bearer <access_token>
```

## 9. MCP管理API

### 9.1 发现MCP服务器
```http
GET /api/v1/mcp/servers/discover
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": [
    {
      "server_id": "filesystem_mcp",
      "name": "文件系统MCP服务",
      "description": "提供文件系统访问能力",
      "server_type": "external",
      "capabilities": {
        "resources": true,
        "tools": true,
        "prompts": false
      },
      "is_public": true,
      "health_status": "healthy"
    }
  ]
}
```

### 8.2 注册MCP服务器
```http
POST /api/v1/mcp/servers
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "自定义MCP服务",
  "description": "我的自定义MCP服务",
  "server_type": "external",
  "connection_config": {
    "transport": "stdio",
    "command": "python",
    "args": ["-m", "my_mcp_server"],
    "env": {
      "API_KEY": "secret"
    }
  },
  "security": {
    "sandbox": true,
    "allowed_paths": ["/workspace"],
    "max_file_size": "10MB"
  }
}
```

### 8.3 连接到MCP服务器
```http
POST /api/v1/mcp/servers/{server_id}/connect
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "config": {
    "timeout": 30,
    "retry_count": 3
  }
}
```

### 8.4 获取MCP资源列表
```http
GET /api/v1/mcp/servers/{server_id}/resources
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "success": true,
  "data": [
    {
      "uri": "file:///workspace/document.txt",
      "name": "文档",
      "description": "工作区文档",
      "mime_type": "text/plain"
    }
  ]
}
```

### 8.5 获取MCP工具列表
```http
GET /api/v1/mcp/servers/{server_id}/tools
Authorization: Bearer <access_token>
```

### 8.6 调用MCP工具
```http
POST /api/v1/mcp/servers/{server_id}/tools/{tool_name}/invoke
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "arguments": {
    "query": "search term",
    "limit": 10
  }
}
```

### 8.7 读取MCP资源
```http
GET /api/v1/mcp/servers/{server_id}/resources/read?uri=file:///path/to/file
Authorization: Bearer <access_token>
```

### 8.8 断开MCP连接
```http
POST /api/v1/mcp/servers/{server_id}/disconnect
Authorization: Bearer <access_token>
```

## 9. 系统管理API

### 9.1 系统状态
```http
GET /api/v1/system/status
Authorization: Bearer <admin_token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "system_status": "healthy",
    "uptime": 86400,
    "version": "1.0.0",
    "agents": {
      "total": 5,
      "running": 3,
      "stopped": 2
    },
    "resources": {
      "cpu_usage": "25%",
      "memory_usage": "512MB",
      "disk_usage": "2GB"
    },
    "database": {
      "status": "connected",
      "connections": 10
    },
    "redis": {
      "status": "connected",
      "memory_usage": "64MB"
    }
  }
}
```

### 8.2 系统日志
```http
GET /api/v1/system/logs?level=error&page=1&size=100
Authorization: Bearer <admin_token>
```

### 8.3 系统配置
```http
GET /api/v1/system/config
PUT /api/v1/system/config
Authorization: Bearer <admin_token>
```

## 9. 错误代码定义

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| CONFLICT | 409 | 资源冲突 |
| RATE_LIMITED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| AGENT_NOT_FOUND | 404 | Agent不存在 |
| AGENT_LOAD_FAILED | 500 | Agent加载失败 |
| AGENT_START_FAILED | 500 | Agent启动失败 |
| CONVERSATION_NOT_FOUND | 404 | 对话不存在 |
| WEBSOCKET_ERROR | 500 | WebSocket连接错误 |

## 10. API安全

### 10.1 认证机制
- JWT Token认证
- Token过期时间：1小时
- Refresh Token有效期：7天
- 支持Token黑名单

### 10.2 权限控制
```python
# 权限装饰器示例
@require_permission("agent:manage")
async def load_agent(agent_id: str, current_user: User = Depends(get_current_user)):
    pass

@require_permission("conversation:read")
async def get_conversations(current_user: User = Depends(get_current_user)):
    pass
```

### 10.3 请求限流
```python
# 限流配置
RATE_LIMITS = {
    "auth": "5/minute",      # 认证接口
    "agent": "10/minute",    # Agent管理接口
    "chat": "60/minute",     # 对话接口
    "upload": "3/minute"     # 文件上传接口
}
```

### 10.4 数据验证
```python
# Pydantic模型示例
class CreateConversationRequest(BaseModel):
    agent_id: str = Field(..., min_length=1, max_length=100)
    title: Optional[str] = Field(None, max_length=200)
    
    @validator('agent_id')
    def validate_agent_id(cls, v):
        if not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('Agent ID格式不正确')
        return v
```

## 11. API文档和测试

### 11.1 Swagger文档
```python
# FastAPI自动生成API文档
app = FastAPI(
    title="AgentsUI API",
    description="AgentsUI后端API文档",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)
```

### 11.2 API测试
```python
# pytest测试示例
async def test_create_conversation(client: AsyncClient, auth_headers):
    response = await client.post(
        "/api/v1/conversations",
        json={
            "agent_id": "culture_agent",
            "title": "测试对话"
        },
        headers=auth_headers
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "conversation_id" in data["data"]
```
