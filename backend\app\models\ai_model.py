from sqlalchemy import Column, String, Boolean, DateTime, Integer, Numeric, Text
from sqlalchemy.dialects.sqlite import VARCHAR
from app.db.base import Base
import uuid
from datetime import datetime

class AIModel(Base):
    __tablename__ = "ai_models"
    
    # 使用VARCHAR代替UUID以兼容SQLite
    model_id = Column(VARCHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    model_name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(200), nullable=False)
    provider = Column(String(50), nullable=False)  # openai, deepseek, kimi, etc.
    model_type = Column(String(50), default="chat")  # chat, completion, embedding
    description = Column(Text)
    api_endpoint = Column(String(500))
    max_tokens = Column(Integer, default=4096)
    supports_streaming = Column(Boolean, default=True)
    supports_functions = Column(Boolean, default=False)
    cost_per_1k_input = Column(Numeric(10, 6), default=0)
    cost_per_1k_output = Column(Numeric(10, 6), default=0)
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)