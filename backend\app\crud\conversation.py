from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Optional, List, Tuple
from app.crud.base import CRUDBase
from app.models.conversation import Conversation
from app.schemas.conversation import ConversationCreate, ConversationUpdate
import uuid

class CRUDConversation(CRUDBase[Conversation, ConversationCreate, ConversationUpdate]):
    def get_multi(
        self, db: Session, *, page: int = 1, size: int = 20, agent_id: str = None
    ) -> <PERSON><PERSON>[List[Conversation], int]:
        query = db.query(Conversation)
        
        if agent_id:
            query = query.filter(Conversation.agent_id == agent_id)
            
        total = query.count()
        offset = (page - 1) * size
        items = query.offset(offset).limit(size).all()
        
        return items, total

    def create(self, db: Session, *, obj_in: ConversationCreate) -> Conversation:
        # 手动创建对象
        db_obj = Conversation()
        db_obj.id = str(uuid.uuid4())
        db_obj.agent_id = obj_in.agent_id
        db_obj.title = obj_in.title
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

conversation = CRUDConversation(Conversation)