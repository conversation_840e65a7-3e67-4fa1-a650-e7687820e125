# AgentsUI 部署运维文档

## 1. 容器化部署

### 1.1 Docker镜像构建

#### 前端Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 后端Dockerfile
```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Agent运行时Dockerfile
```dockerfile
# agents/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装MCP相关依赖
RUN pip install mcp langchain langgraph fastapi uvicorn

# 复制Agent代码
COPY agents/ ./agents/

# 设置环境变量
ENV PYTHONPATH=/app

EXPOSE 8001-8010
CMD ["python", "-m", "agents.runner"]
```

### 1.2 Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    networks:
      - agentsui-network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    environment:
      - DATABASE_URL=********************************************/agentsui
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./agents:/app/agents:ro
      - ./uploads:/app/uploads
    networks:
      - agentsui-network

  # Agent运行时
  agent-runtime:
    build:
      context: .
      dockerfile: agents/Dockerfile
    ports:
      - "8001-8010:8001-8010"
    depends_on:
      - backend
    environment:
      - BACKEND_URL=http://backend:8000
    volumes:
      - ./agents:/app/agents
      - ./data:/app/data
    networks:
      - agentsui-network

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=agentsui
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1qaz@wsX
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - agentsui-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - agentsui-network

  # MCP服务示例
  mcp-filesystem:
    image: mcp-filesystem:latest
    environment:
      - FILESYSTEM_ROOT=/workspace
    volumes:
      - ./workspace:/workspace
    networks:
      - agentsui-network

volumes:
  postgres_data:
  redis_data:

networks:
  agentsui-network:
    driver: bridge
```

### 1.3 生产环境配置

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - agentsui-network

  frontend:
    extends:
      file: docker-compose.yml
      service: frontend
    environment:
      - REACT_APP_API_URL=https://api.agentsui.com
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  backend:
    extends:
      file: docker-compose.yml
      service: backend
    environment:
      - DATABASE_URL=********************************************/agentsui
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - LOG_LEVEL=INFO
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  postgres:
    extends:
      file: docker-compose.yml
      service: postgres
    environment:
      - POSTGRES_PASSWORD=1qaz@wsX
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'
        reservations:
          memory: 1G
          cpus: '0.5'
```

## 2. CI/CD流程

### 2.1 GitHub Actions配置

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install Python dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        pip install pytest pytest-asyncio
    
    - name: Install Node.js dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run Python tests
      run: |
        cd backend
        pytest tests/ -v
      env:
        DATABASE_URL: postgresql://postgres:test@localhost:5432/test
        REDIS_URL: redis://localhost:6379
    
    - name: Run Frontend tests
      run: |
        cd frontend
        npm test -- --coverage --watchAll=false
    
    - name: Build Frontend
      run: |
        cd frontend
        npm run build

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push Frontend image
      uses: docker/build-push-action@v4
      with:
        context: ./frontend
        push: true
        tags: agentsui/frontend:latest
    
    - name: Build and push Backend image
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        push: true
        tags: agentsui/backend:latest
    
    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /opt/agentsui
          docker-compose -f docker-compose.prod.yml pull
          docker-compose -f docker-compose.prod.yml up -d
          docker system prune -f
```

### 2.2 部署脚本

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

echo "开始部署AgentsUI..."

# 检查环境变量
if [ -z "$ENVIRONMENT" ]; then
    echo "错误: 未设置ENVIRONMENT环境变量"
    exit 1
fi

# 拉取最新代码
git pull origin main

# 构建镜像
echo "构建Docker镜像..."
docker-compose -f docker-compose.${ENVIRONMENT}.yml build

# 停止旧服务
echo "停止旧服务..."
docker-compose -f docker-compose.${ENVIRONMENT}.yml down

# 数据库迁移
echo "执行数据库迁移..."
docker-compose -f docker-compose.${ENVIRONMENT}.yml run --rm backend alembic upgrade head

# 启动新服务
echo "启动新服务..."
docker-compose -f docker-compose.${ENVIRONMENT}.yml up -d

# 健康检查
echo "执行健康检查..."
sleep 30
curl -f http://localhost:8000/health || exit 1

echo "部署完成!"
```

## 3. 监控告警

### 3.1 Prometheus配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'agentsui-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'agentsui-agents'
    static_configs:
      - targets: ['agent-runtime:8001']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 3.2 Grafana仪表板

```json
{
  "dashboard": {
    "title": "AgentsUI监控面板",
    "panels": [
      {
        "title": "API请求率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Agent状态",
        "type": "stat",
        "targets": [
          {
            "expr": "agent_status",
            "legendFormat": "{{agent_id}}"
          }
        ]
      },
      {
        "title": "MCP连接状态",
        "type": "table",
        "targets": [
          {
            "expr": "mcp_connection_status",
            "legendFormat": "{{server_id}}"
          }
        ]
      }
    ]
  }
}
```

### 3.3 告警规则

```yaml
# monitoring/alert_rules.yml
groups:
  - name: agentsui_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "高错误率告警"
          description: "API错误率超过10%"

      - alert: AgentDown
        expr: agent_status == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Agent离线"
          description: "Agent {{ $labels.agent_id }} 已离线"

      - alert: MCPConnectionLost
        expr: mcp_connection_status == 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "MCP连接丢失"
          description: "MCP服务器 {{ $labels.server_id }} 连接丢失"

      - alert: DatabaseConnectionFailed
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "数据库连接失败"
          description: "PostgreSQL数据库连接失败"
```

## 4. 性能优化

### 4.1 数据库优化

```sql
-- 数据库性能优化配置
-- postgresql.conf

# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# 连接配置
max_connections = 100
shared_preload_libraries = 'pg_stat_statements'

# 日志配置
log_statement = 'all'
log_duration = on
log_min_duration_statement = 1000

# 检查点配置
checkpoint_completion_target = 0.9
wal_buffers = 16MB
```

### 4.2 Redis优化

```conf
# redis.conf

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 网络配置
tcp-keepalive 300
timeout 0
```

### 4.3 应用层优化

```python
# backend/core/performance.py

import asyncio
from functools import wraps
from typing import Dict, Any
import time

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
    
    def track_execution_time(self, func_name: str):
        """装饰器：跟踪函数执行时间"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    execution_time = time.time() - start_time
                    self.record_metric(func_name, execution_time)
            return wrapper
        return decorator
    
    def record_metric(self, name: str, value: float):
        """记录性能指标"""
        if name not in self.metrics:
            self.metrics[name] = []
        self.metrics[name].append(value)
        
        # 保持最近1000条记录
        if len(self.metrics[name]) > 1000:
            self.metrics[name] = self.metrics[name][-1000:]

# 连接池优化
from sqlalchemy.pool import QueuePool

engine = create_async_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

## 5. 备份恢复

### 5.1 数据库备份

```bash
#!/bin/bash
# scripts/backup.sh

BACKUP_DIR="/backup/postgres"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="agentsui"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 全量备份
pg_dump -h postgres -U postgres -d $DB_NAME > $BACKUP_DIR/full_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/full_backup_$DATE.sql

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "备份完成: full_backup_$DATE.sql.gz"
```

### 5.2 文件备份

```bash
#!/bin/bash
# scripts/backup_files.sh

BACKUP_DIR="/backup/files"
DATE=$(date +%Y%m%d_%H%M%S)

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /app/uploads

# 备份Agent代码
tar -czf $BACKUP_DIR/agents_$DATE.tar.gz /app/agents

# 备份配置文件
tar -czf $BACKUP_DIR/configs_$DATE.tar.gz /app/configs

echo "文件备份完成"
```

### 5.3 恢复脚本

```bash
#!/bin/bash
# scripts/restore.sh

if [ $# -ne 1 ]; then
    echo "用法: $0 <backup_file>"
    exit 1
fi

BACKUP_FILE=$1

# 停止服务
docker-compose down

# 恢复数据库
gunzip -c $BACKUP_FILE | docker-compose run --rm postgres psql -h postgres -U postgres -d agentsui

# 重启服务
docker-compose up -d

echo "恢复完成"
```

## 6. 安全配置

### 6.1 SSL/TLS配置

```nginx
# nginx/nginx.conf
server {
    listen 443 ssl http2;
    server_name agentsui.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 6.2 防火墙配置

```bash
# scripts/firewall.sh

# 清空现有规则
iptables -F

# 默认策略
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# 允许本地回环
iptables -A INPUT -i lo -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 允许SSH
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# 允许HTTP/HTTPS
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# 保存规则
iptables-save > /etc/iptables/rules.v4
```
