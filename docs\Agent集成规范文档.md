# AgentsUI Agent集成规范文档

## 1. 概述

AgentsUI 支持两种类型的Agent集成：
1. **原生Agent**: 基于LangChain/LangGraph开发的Agent，使用AG-UI协议
2. **MCP服务**: 基于Model Context Protocol的第三方服务

本文档定义了Agent接入标准、协议实现规范和生命周期管理。

## 2. 原生Agent集成规范

### 2.1 Agent目录结构

```
agents/
├── {agent_name}/
│   ├── agent.py          # Agent主类，实现LangGraph状态机
│   ├── schema.py         # 数据模型定义
│   ├── tools.py          # Agent工具函数
│   ├── main.py           # FastAPI + WebSocket服务
│   ├── requirements.txt  # 依赖列表
│   ├── config.json       # Agent配置文件
│   ├── README.md         # 说明文档
│   └── prompts/          # 提示词模板
│       ├── system.py
│       └── user.py
```

### 2.2 Agent配置文件 (config.json)

```json
{
  "agent_id": "culture_agent",
  "name": "企业文化精神符号总结Agent",
  "version": "1.0.0",
  "description": "帮助分析企业文化并生成精神符号",
  "author": "AgentsUI Team",
  "tags": ["文化分析", "符号生成"],
  "entry_point": "agent.CultureSymbolAgent",
  "config_schema": {
    "type": "object",
    "properties": {
      "openai_api_key": {
        "type": "string",
        "description": "OpenAI API密钥"
      },
      "model_name": {
        "type": "string",
        "default": "deepseek-chat",
        "description": "使用的模型名称"
      }
    },
    "required": ["openai_api_key"]
  },
  "capabilities": {
    "supports_streaming": true,
    "supports_interruption": true,
    "supports_context": true,
    "max_context_length": 4000
  },
  "mcp_integration": {
    "supports_mcp": true,
    "mcp_client_config": {
      "max_connections": 5,
      "timeout": 30
    }
  },
  "model_integration": {
    "supports_multiple_models": true,
    "default_model": "deepseek",
    "supported_models": ["deepseek", "kimi-k2", "gpt-4"],
    "model_switching": {
      "runtime_switching": true,
      "fallback_enabled": true
    }
  }
}
```

### 2.3 AG-UI协议规范

#### 消息格式
```json
{
  "type": "message|question|confirmation|symbols|error|mcp_request",
  "content": "消息内容",
  "data": {
    "step": "当前步骤",
    "waiting_for_user": true,
    "input_type": "text|confirmation|answers|mcp_config",
    "mcp_data": {
      "server_id": "mcp_server_uuid",
      "resource_uri": "file://path/to/resource",
      "tool_name": "search_tool"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### WebSocket事件类型
- `user_message`: 用户消息
- `agent_response`: Agent响应
- `agent_status`: Agent状态变化
- `mcp_event`: MCP相关事件
- `error`: 错误消息

### 2.4 Agent基类接口

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from schema import AgentState, MCPServerConfig

class BaseAgent(ABC):
    """Agent基类，所有Agent必须继承此类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mcp_clients = {}
    
    @abstractmethod
    async def process_user_input(self, state: AgentState, user_input: str, input_type: str = "text") -> AgentState:
        """处理用户输入"""
        pass
    
    @abstractmethod
    async def get_current_response(self, state: AgentState) -> Dict[str, Any]:
        """获取当前响应"""
        pass
    
    # MCP集成方法
    async def connect_mcp_server(self, server_config: MCPServerConfig) -> bool:
        """连接MCP服务器"""
        pass
    
    async def disconnect_mcp_server(self, server_id: str) -> bool:
        """断开MCP服务器连接"""
        pass
    
    async def list_mcp_resources(self, server_id: str) -> List[Dict]:
        """列出MCP资源"""
        pass
    
    async def get_mcp_resource(self, server_id: str, resource_uri: str) -> Any:
        """获取MCP资源"""
        pass
    
    async def invoke_mcp_tool(self, server_id: str, tool_name: str, params: Dict) -> Any:
        """调用MCP工具"""
        pass

    # 模型集成方法
    async def set_model(self, model_id: str, config_id: Optional[str] = None) -> bool:
        """设置Agent使用的模型"""
        pass

    async def get_current_model(self) -> Dict[str, Any]:
        """获取当前使用的模型信息"""
        pass

    async def switch_model(self, model_id: str, config_id: Optional[str] = None) -> bool:
        """运行时切换模型"""
        pass

    async def test_model_connection(self, model_id: str) -> bool:
        """测试模型连接"""
        pass

    async def get_model_usage_stats(self) -> Dict[str, Any]:
        """获取模型使用统计"""
        pass
```

## 3. MCP服务集成规范

### 3.1 MCP服务器配置

```json
{
  "server_id": "filesystem_mcp",
  "name": "文件系统MCP服务",
  "description": "提供文件系统访问能力",
  "server_type": "external",
  "connection_config": {
    "transport": "stdio",
    "command": "python",
    "args": ["-m", "mcp_filesystem"],
    "env": {
      "FILESYSTEM_ROOT": "/workspace"
    }
  },
  "capabilities": {
    "resources": true,
    "tools": true,
    "prompts": false,
    "sampling": false
  },
  "security": {
    "sandbox": true,
    "allowed_paths": ["/workspace", "/tmp"],
    "max_file_size": "10MB"
  }
}
```

### 3.2 MCP连接类型

#### 3.2.1 标准输入输出 (stdio)
```python
{
  "transport": "stdio",
  "command": "python",
  "args": ["-m", "mcp_server"],
  "cwd": "/path/to/server",
  "env": {"KEY": "value"}
}
```

#### 3.2.2 HTTP/WebSocket
```python
{
  "transport": "http",
  "url": "http://localhost:8080/mcp",
  "headers": {
    "Authorization": "Bearer token"
  }
}
```

#### 3.2.3 Docker容器
```python
{
  "transport": "docker",
  "image": "mcp-server:latest",
  "ports": {"8080": "8080"},
  "volumes": {"/data": "/container/data"},
  "env": {"CONFIG": "production"}
}
```

### 3.3 MCP客户端实现

```python
import asyncio
import json
from typing import Dict, Any, List, Optional
from mcp import ClientSession, StdioServerParameters

class MCPClient:
    """MCP客户端封装"""
    
    def __init__(self, server_config: Dict[str, Any]):
        self.server_config = server_config
        self.session: Optional[ClientSession] = None
        self.connected = False
    
    async def connect(self) -> bool:
        """连接到MCP服务器"""
        try:
            if self.server_config["transport"] == "stdio":
                server_params = StdioServerParameters(
                    command=self.server_config["command"],
                    args=self.server_config.get("args", []),
                    env=self.server_config.get("env", {})
                )
                self.session = await ClientSession.create(server_params)
                
            elif self.server_config["transport"] == "http":
                # HTTP连接实现
                pass
                
            self.connected = True
            return True
            
        except Exception as e:
            print(f"MCP连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.session:
            await self.session.close()
            self.connected = False
    
    async def list_resources(self) -> List[Dict]:
        """列出可用资源"""
        if not self.connected:
            return []
        
        try:
            result = await self.session.list_resources()
            return [resource.dict() for resource in result.resources]
        except Exception as e:
            print(f"列出资源失败: {e}")
            return []
    
    async def read_resource(self, uri: str) -> Optional[str]:
        """读取资源内容"""
        if not self.connected:
            return None
        
        try:
            result = await self.session.read_resource(uri)
            return result.contents[0].text if result.contents else None
        except Exception as e:
            print(f"读取资源失败: {e}")
            return None
    
    async def list_tools(self) -> List[Dict]:
        """列出可用工具"""
        if not self.connected:
            return []
        
        try:
            result = await self.session.list_tools()
            return [tool.dict() for tool in result.tools]
        except Exception as e:
            print(f"列出工具失败: {e}")
            return []
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> Any:
        """调用工具"""
        if not self.connected:
            return None
        
        try:
            result = await self.session.call_tool(name, arguments)
            return result.content
        except Exception as e:
            print(f"调用工具失败: {e}")
            return None
```

## 4. Agent生命周期管理

### 4.1 生命周期状态

```python
class AgentStatus(str, Enum):
    DISCOVERED = "discovered"    # 已发现
    LOADING = "loading"         # 加载中
    LOADED = "loaded"           # 已加载
    STARTING = "starting"       # 启动中
    RUNNING = "running"         # 运行中
    STOPPING = "stopping"       # 停止中
    STOPPED = "stopped"         # 已停止
    ERROR = "error"             # 错误状态
    UNLOADING = "unloading"     # 卸载中
```

### 4.2 生命周期管理器

```python
class AgentLifecycleManager:
    """Agent生命周期管理器"""
    
    async def discover_agents(self) -> List[AgentInfo]:
        """发现可用的Agent"""
        agents = []
        agents_dir = Path("agents")
        
        for agent_path in agents_dir.iterdir():
            if agent_path.is_dir():
                config_file = agent_path / "config.json"
                if config_file.exists():
                    with open(config_file) as f:
                        config = json.load(f)
                    
                    agent_info = AgentInfo(
                        agent_id=config["agent_id"],
                        name=config["name"],
                        version=config["version"],
                        description=config["description"],
                        path=str(agent_path),
                        config_schema=config.get("config_schema", {}),
                        status=AgentStatus.DISCOVERED
                    )
                    agents.append(agent_info)
        
        return agents
    
    async def load_agent(self, agent_id: str, config: Dict[str, Any]) -> bool:
        """加载Agent"""
        try:
            # 更新状态
            await self.update_agent_status(agent_id, AgentStatus.LOADING)
            
            # 动态导入Agent模块
            agent_module = importlib.import_module(f"agents.{agent_id}.agent")
            agent_class = getattr(agent_module, config["entry_point"].split(".")[-1])
            
            # 创建Agent实例
            agent_instance = agent_class(config)
            
            # 存储实例
            self.agent_instances[agent_id] = agent_instance
            
            # 更新状态
            await self.update_agent_status(agent_id, AgentStatus.LOADED)
            return True
            
        except Exception as e:
            await self.update_agent_status(agent_id, AgentStatus.ERROR)
            raise e
    
    async def start_agent(self, agent_id: str) -> bool:
        """启动Agent"""
        if agent_id not in self.agent_instances:
            return False
        
        try:
            await self.update_agent_status(agent_id, AgentStatus.STARTING)
            
            # 启动Agent进程
            agent = self.agent_instances[agent_id]
            await agent.start()
            
            await self.update_agent_status(agent_id, AgentStatus.RUNNING)
            return True
            
        except Exception as e:
            await self.update_agent_status(agent_id, AgentStatus.ERROR)
            return False
```

## 5. 安全和权限控制

### 5.1 Agent权限模型

```python
class AgentPermissions:
    """Agent权限定义"""
    
    # 文件系统权限
    FILE_READ = "file:read"
    FILE_WRITE = "file:write"
    FILE_EXECUTE = "file:execute"
    
    # 网络权限
    NETWORK_HTTP = "network:http"
    NETWORK_WEBSOCKET = "network:websocket"
    
    # MCP权限
    MCP_CONNECT = "mcp:connect"
    MCP_RESOURCE_READ = "mcp:resource:read"
    MCP_TOOL_INVOKE = "mcp:tool:invoke"
    
    # 系统权限
    SYSTEM_PROCESS = "system:process"
    SYSTEM_ENV = "system:env"
```

### 5.2 MCP安全策略

```python
class MCPSecurityPolicy:
    """MCP安全策略"""
    
    def __init__(self, config: Dict[str, Any]):
        self.sandbox_enabled = config.get("sandbox", True)
        self.allowed_paths = config.get("allowed_paths", [])
        self.max_file_size = config.get("max_file_size", "10MB")
        self.timeout = config.get("timeout", 30)
    
    def validate_resource_access(self, uri: str) -> bool:
        """验证资源访问权限"""
        if not self.sandbox_enabled:
            return True
        
        # 检查路径是否在允许列表中
        for allowed_path in self.allowed_paths:
            if uri.startswith(allowed_path):
                return True
        
        return False
    
    def validate_tool_invocation(self, tool_name: str, params: Dict) -> bool:
        """验证工具调用权限"""
        # 实现工具调用验证逻辑
        return True
```

## 6. 错误处理和监控

### 6.1 错误类型定义

```python
class AgentError(Exception):
    """Agent错误基类"""
    pass

class AgentLoadError(AgentError):
    """Agent加载错误"""
    pass

class AgentRuntimeError(AgentError):
    """Agent运行时错误"""
    pass

class MCPConnectionError(AgentError):
    """MCP连接错误"""
    pass

class MCPSecurityError(AgentError):
    """MCP安全错误"""
    pass
```

### 6.2 监控指标

```python
class AgentMetrics:
    """Agent监控指标"""
    
    def __init__(self):
        self.start_time = time.time()
        self.message_count = 0
        self.error_count = 0
        self.mcp_call_count = 0
        self.response_times = []
    
    def record_message(self, response_time: float):
        """记录消息处理"""
        self.message_count += 1
        self.response_times.append(response_time)
    
    def record_error(self):
        """记录错误"""
        self.error_count += 1
    
    def record_mcp_call(self):
        """记录MCP调用"""
        self.mcp_call_count += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime = time.time() - self.start_time
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
        
        return {
            "uptime": uptime,
            "message_count": self.message_count,
            "error_count": self.error_count,
            "mcp_call_count": self.mcp_call_count,
            "avg_response_time": avg_response_time,
            "error_rate": self.error_count / max(self.message_count, 1)
        }
```

## 7. 测试规范

### 7.1 Agent单元测试

```python
import pytest
from unittest.mock import Mock, AsyncMock
from agents.culture_agent.agent import CultureSymbolAgent

class TestCultureSymbolAgent:
    
    @pytest.fixture
    async def agent(self):
        config = {
            "openai_api_key": "test-key",
            "model_name": "test-model"
        }
        return CultureSymbolAgent(config)
    
    async def test_process_user_input(self, agent):
        """测试用户输入处理"""
        state = AgentState()
        result = await agent.process_user_input(state, "腾讯公司")
        
        assert result.company_info is not None
        assert result.company_info.name == "腾讯公司"
    
    async def test_mcp_integration(self, agent):
        """测试MCP集成"""
        # Mock MCP客户端
        mock_client = AsyncMock()
        agent.mcp_clients["test_server"] = mock_client
        
        # 测试资源获取
        mock_client.read_resource.return_value = "test content"
        result = await agent.get_mcp_resource("test_server", "file://test.txt")
        
        assert result == "test content"
        mock_client.read_resource.assert_called_once_with("file://test.txt")
```

### 7.2 MCP集成测试

```python
class TestMCPIntegration:
    
    async def test_mcp_connection(self):
        """测试MCP连接"""
        config = {
            "transport": "stdio",
            "command": "python",
            "args": ["-m", "test_mcp_server"]
        }
        
        client = MCPClient(config)
        connected = await client.connect()
        
        assert connected is True
        assert client.connected is True
        
        await client.disconnect()
        assert client.connected is False
    
    async def test_resource_access(self):
        """测试资源访问"""
        # 实现资源访问测试
        pass
    
    async def test_tool_invocation(self):
        """测试工具调用"""
        # 实现工具调用测试
        pass
```
