from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from uuid import UUID

class ConversationBase(BaseModel):
    title: Optional[str] = Field(None, max_length=500)
    agent_id: str = Field(..., max_length=100)

class ConversationCreate(ConversationBase):
    pass

class ConversationUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=500)
    status: Optional[str] = None

class Conversation(ConversationBase):
    conversation_id: UUID
    user_id: UUID
    instance_id: Optional[UUID] = None
    status: str = "active"
    context: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    message_count: int = 0
    last_message_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class MessageBase(BaseModel):
    content: str
    message_type: str = "text"
    data: Optional[Dict[str, Any]] = None

class MessageCreate(MessageBase):
    conversation_id: UUID
    sender: str = Field(..., pattern="^(user|agent|system)$")

class MessageUpdate(BaseModel):
    content: str
    is_edited: bool = True

class Message(MessageBase):
    message_id: UUID
    conversation_id: UUID
    sender: str
    parent_message_id: Optional[UUID] = None
    is_edited: bool = False
    edit_count: int = 0
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True