{"summary": {"total_tests": 15, "successful_tests": 9, "failed_tests": 6, "success_rate": "60.0%", "test_time": "2025-08-02T13:10:22.579750"}, "results": [{"name": "根路径", "method": "GET", "endpoint": "/", "status_code": 200, "success": true, "timestamp": "2025-08-02T13:10:22.321143", "request_data": null, "response_data": {"message": "Welcome to AgentsUI API"}, "error": null}, {"name": "健康检查", "method": "GET", "endpoint": "/health", "status_code": 200, "success": true, "timestamp": "2025-08-02T13:10:22.323050", "request_data": null, "response_data": {"status": "healthy"}, "error": null}, {"name": "API文档", "method": "GET", "endpoint": "/docs", "status_code": 200, "success": true, "timestamp": "2025-08-02T13:10:22.324683", "request_data": null, "response_data": null, "error": null}, {"name": "用户注册", "method": "POST", "endpoint": "/api/v1/auth/register", "status_code": 200, "success": true, "timestamp": "2025-08-02T13:10:22.416237", "request_data": {"username": "testuser_1754111422", "email": "<EMAIL>", "password": "testpassword123", "full_name": "Test User"}, "response_data": {"success": true, "message": "用户注册成功", "timestamp": "2025-08-02T05:10:22.414793", "data": {"username": "testuser_1754111422", "email": "<EMAIL>", "full_name": "Test User", "user_id": "65d62ec6-38ba-4ce4-a154-3127069e1b2c", "avatar_url": null, "is_active": true, "is_verified": false, "last_login_at": null, "created_at": "2025-08-02T05:10:22.407613"}}, "error": null}, {"name": "用户登录", "method": "POST", "endpoint": "/api/v1/auth/login", "status_code": 200, "success": true, "timestamp": "2025-08-02T13:10:22.502468", "request_data": {"username": "testuser_1754111422", "password": "testpassword123"}, "response_data": {"success": true, "message": "登录成功", "timestamp": "2025-08-02T05:10:22.500174", "data": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NWQ2MmVjNi0zOGJhLTRjZTQtYTE1NC0zMTI3MDY5ZTFiMmMiLCJleHAiOjE3NTQxMTUwMjJ9.ZBoGtwnOpC7u6g8LT66f1-P00xZwmIXmKY55xd2fAoo", "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NWQ2MmVjNi0zOGJhLTRjZTQtYTE1NC0zMTI3MDY5ZTFiMmMiLCJleHAiOjE3NTQ3MTYyMjJ9.fxdpKqg0sOdpeZT02ok_3PB1rA8qf5xIgoay_UJpJPw", "token_type": "bearer", "expires_in": 3600, "user": {"username": "testuser_1754111422", "email": "<EMAIL>", "full_name": "Test User", "user_id": "65d62ec6-38ba-4ce4-a154-3127069e1b2c", "avatar_url": null, "is_active": true, "is_verified": false, "last_login_at": "2025-08-02T05:10:22.493081", "created_at": "2025-08-02T05:10:22.407613"}}}, "error": null}, {"name": "获取模型列表", "method": "GET", "endpoint": "/api/v1/models", "status_code": 200, "success": true, "timestamp": "2025-08-02T13:10:22.509516", "request_data": null, "response_data": {"success": true, "message": "操作成功", "timestamp": "2025-08-02T05:10:22.507757", "data": [{"model_name": "test_model_1754111398", "display_name": "Test Model", "provider": "test_provider", "model_type": "chat", "description": "Test model for API testing", "api_endpoint": null, "max_tokens": 4096, "supports_streaming": true, "supports_functions": false, "cost_per_1k_input": "0.000000", "cost_per_1k_output": "0.000000", "model_id": "19685676-d29e-4d8b-9177-0c07a72e4ad2", "is_active": true, "is_default": false, "created_at": "2025-08-02T05:09:58.148419", "updated_at": "2025-08-02T05:09:58.148421"}]}, "error": null}, {"name": "添加新模型", "method": "POST", "endpoint": "/api/v1/models", "status_code": 200, "success": true, "timestamp": "2025-08-02T13:10:22.522831", "request_data": {"model_name": "test_model_1754111422", "display_name": "Test Model", "provider": "test_provider", "model_type": "chat", "description": "Test model for API testing"}, "response_data": {"success": true, "message": "操作成功", "timestamp": "2025-08-02T05:10:22.521122", "data": {"model_name": "test_model_1754111422", "display_name": "Test Model", "provider": "test_provider", "model_type": "chat", "description": "Test model for API testing", "api_endpoint": null, "max_tokens": 4096, "supports_streaming": true, "supports_functions": false, "cost_per_1k_input": "0.000000", "cost_per_1k_output": "0.000000", "model_id": "271b2a1b-2d4e-4017-98c2-123e8bf0282a", "is_active": true, "is_default": false, "created_at": "2025-08-02T05:10:22.514794", "updated_at": "2025-08-02T05:10:22.514797"}}, "error": null}, {"name": "更新模型", "method": "PUT", "endpoint": "/api/v1/models/271b2a1b-2d4e-4017-98c2-123e8bf0282a", "status_code": 0, "success": false, "timestamp": "2025-08-02T13:10:22.530099", "request_data": null, "response_data": null, "error": "Expecting value: line 1 column 1 (char 0)"}, {"name": "删除模型", "method": "DELETE", "endpoint": "/api/v1/models/271b2a1b-2d4e-4017-98c2-123e8bf0282a", "status_code": 0, "success": false, "timestamp": "2025-08-02T13:10:22.538511", "request_data": null, "response_data": null, "error": "Expecting value: line 1 column 1 (char 0)"}, {"name": "发现可用Agents", "method": "GET", "endpoint": "/api/v1/agents/discover", "status_code": 200, "success": true, "timestamp": "2025-08-02T13:10:22.544524", "request_data": null, "response_data": {"success": true, "message": "操作成功", "timestamp": "2025-08-02T05:10:22.543083", "data": []}, "error": null}, {"name": "获取已加载Agents", "method": "GET", "endpoint": "/api/v1/agents/loaded", "status_code": 0, "success": false, "timestamp": "2025-08-02T13:10:22.554477", "request_data": null, "response_data": null, "error": "Expecting value: line 1 column 1 (char 0)"}, {"name": "获取对话列表", "method": "GET", "endpoint": "/api/v1/conversations", "status_code": 200, "success": true, "timestamp": "2025-08-02T13:10:22.561778", "request_data": null, "response_data": {"success": true, "message": "操作成功", "timestamp": "2025-08-02T05:10:22.559977", "data": {"items": [], "pagination": {"page": 1, "size": 20, "total": 0, "pages": 0}}}, "error": null}, {"name": "创建对话", "method": "POST", "endpoint": "/api/v1/conversations", "status_code": 500, "success": false, "timestamp": "2025-08-02T13:10:22.573338", "request_data": {"title": "Test Conversation", "agent_id": "test_agent_id"}, "response_data": {"detail": "服务器内部错误: (sqlite3.IntegrityError) NOT NULL constraint failed: conversations.conversation_id\n[SQL: INSERT INTO conversations (user_id, agent_id, title, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)]\n[parameters: (None, 'test_agent_id', 'Test Conversation', 'active', '2025-08-02 05:10:22.565950', '2025-08-02 05:10:22.565953')]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)"}, "error": null}, {"name": "不存在的接口", "method": "GET", "endpoint": "/api/v1/nonexistent", "status_code": 404, "success": false, "timestamp": "2025-08-02T13:10:22.575349", "request_data": null, "response_data": {"detail": "Not Found"}, "error": null}, {"name": "无效JSON数据", "method": "POST", "endpoint": "/api/v1/models", "status_code": 422, "success": false, "timestamp": "2025-08-02T13:10:22.579462", "request_data": null, "response_data": {"detail": [{"type": "json_invalid", "loc": ["body", 0], "msg": "JSON decode error", "input": {}, "ctx": {"error": "Expecting value"}}]}, "error": null}]}