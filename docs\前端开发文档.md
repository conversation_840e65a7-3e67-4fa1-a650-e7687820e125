# AgentsUI 前端开发文档

## 1. 项目结构

```
frontend/
├── public/
│   ├── index.html
│   ├── favicon.ico
│   └── manifest.json
├── src/
│   ├── components/          # 通用组件
│   │   ├── Layout/         # 布局组件
│   │   ├── Navigation/     # 导航组件
│   │   ├── Chat/          # 对话组件
│   │   ├── AgentList/     # Agent列表组件
│   │   ├── KnowledgeBase/ # 知识库组件
│   │   ├── Settings/      # 设置组件
│   │   ├── MCP/          # MCP管理组件
│   │   ├── Models/       # 模型管理组件
│   │   └── Common/        # 通用UI组件
│   ├── pages/             # 页面组件
│   ├── hooks/             # 自定义Hooks
│   ├── store/             # 状态管理
│   ├── services/          # API服务
│   ├── types/             # TypeScript类型定义
│   ├── utils/             # 工具函数
│   ├── styles/            # 样式文件
│   └── constants/         # 常量定义
├── package.json
├── tsconfig.json
├── tailwind.config.js
└── vite.config.ts
```

## 2. 界面布局设计

### 2.1 整体布局结构

```tsx
// Layout/MainLayout.tsx
interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <div className="h-screen flex flex-col">
      {/* 顶部菜单区域 */}
      <Header />
      
      {/* 主内容区域 - 四列布局 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧导航 */}
        <Navigation />
        
        {/* 主内容区域 */}
        <main className="flex-1 flex">
          {children}
        </main>
      </div>
    </div>
  );
};
```

### 2.2 顶部菜单区域 (Header)

```tsx
// components/Layout/Header.tsx
const Header: React.FC = () => {
  const { user, theme, setTheme } = useAppStore();
  const { currentPath } = useNavigation();

  return (
    <header className="h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex items-center px-4">
      {/* Logo */}
      <div className="flex items-center space-x-3">
        <img src="/logo.svg" alt="AgentsUI" className="h-8 w-8" />
        <span className="text-xl font-semibold text-gray-900 dark:text-white">
          AgentsUI
        </span>
      </div>

      {/* 当前导航路径 + 标题 */}
      <div className="flex-1 flex items-center ml-8">
        <Breadcrumb path={currentPath} />
      </div>

      {/* 右侧功能区 */}
      <div className="flex items-center space-x-4">
        {/* 主题切换 */}
        <ThemeToggle theme={theme} onThemeChange={setTheme} />
        
        {/* 用户信息 */}
        <UserProfile user={user} />
      </div>
    </header>
  );
};
```

### 2.3 四列主区域布局

```tsx
// components/Layout/MainContent.tsx
const MainContent: React.FC = () => {
  const { activeTab } = useNavigation();

  return (
    <div className="flex-1 flex">
      {/* 第二列：列表区域 */}
      <div className="w-80 border-r border-gray-200 dark:border-gray-700">
        {activeTab === 'agents' && <AgentList />}
        {activeTab === 'knowledge' && <KnowledgeCategories />}
        {activeTab === 'settings' && <SettingsCategories />}
      </div>

      {/* 第三列：内容区域 */}
      <div className="flex-1 flex flex-col">
        {activeTab === 'agents' && <ChatArea />}
        {activeTab === 'knowledge' && <KnowledgeList />}
        {activeTab === 'settings' && <SettingsPanel />}
      </div>

      {/* 第四列：工具区域（仅在Agents选中时显示） */}
      {activeTab === 'agents' && (
        <div className="w-80 border-l border-gray-200 dark:border-gray-700">
          <AgentToolsPanel />
        </div>
      )}
    </div>
  );
};
```

## 3. 核心组件设计

### 3.1 导航组件 (Navigation)

```tsx
// components/Navigation/Navigation.tsx
interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType;
  path: string;
}

const Navigation: React.FC = () => {
  const { activeTab, setActiveTab } = useNavigation();
  const [collapsed, setCollapsed] = useState(false);

  const navigationItems: NavigationItem[] = [
    { id: 'agents', label: 'Agents', icon: RobotIcon, path: '/agents' },
    { id: 'knowledge', label: '知识库', icon: BookIcon, path: '/knowledge' },
    { id: 'settings', label: '设置', icon: SettingsIcon, path: '/settings' },
  ];

  return (
    <nav className={`bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ${
      collapsed ? 'w-16' : 'w-64'
    }`}>
      {/* 折叠按钮 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="w-full flex items-center justify-center p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <MenuIcon className="h-5 w-5" />
        </button>
      </div>

      {/* 导航项 */}
      <div className="p-4 space-y-2">
        {navigationItems.map((item) => (
          <NavigationItem
            key={item.id}
            item={item}
            active={activeTab === item.id}
            collapsed={collapsed}
            onClick={() => setActiveTab(item.id)}
          />
        ))}
      </div>
    </nav>
  );
};
```

### 3.2 Agent列表组件

```tsx
// components/AgentList/AgentList.tsx
const AgentList: React.FC = () => {
  const { agents, loadedAgents, loadAgent, selectAgent } = useAgentStore();
  const [showAddModal, setShowAddModal] = useState(false);

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Agents
          </h2>
          <button
            onClick={() => setShowAddModal(true)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
          >
            <PlusIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Agent列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {loadedAgents.map((agent) => (
          <AgentCard
            key={agent.id}
            agent={agent}
            onClick={() => selectAgent(agent.id)}
          />
        ))}
      </div>

      {/* 添加Agent模态框 */}
      <AddAgentModal
        open={showAddModal}
        onClose={() => setShowAddModal(false)}
        availableAgents={agents.filter(a => !loadedAgents.find(la => la.id === a.id))}
        onLoadAgent={loadAgent}
      />
    </div>
  );
};
```

### 3.3 MCP管理组件

```tsx
// components/MCP/MCPServerList.tsx
const MCPServerList: React.FC = () => {
  const { mcpServers, connectToServer, disconnectFromServer } = useMCPStore();
  const [showAddModal, setShowAddModal] = useState(false);

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            MCP服务器
          </h2>
          <button
            onClick={() => setShowAddModal(true)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
          >
            <PlusIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* 服务器列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {mcpServers.map((server) => (
          <MCPServerCard
            key={server.server_id}
            server={server}
            onConnect={() => connectToServer(server.server_id)}
            onDisconnect={() => disconnectFromServer(server.server_id)}
          />
        ))}
      </div>

      {/* 添加服务器模态框 */}
      <AddMCPServerModal
        open={showAddModal}
        onClose={() => setShowAddModal(false)}
      />
    </div>
  );
};
```

```tsx
// components/MCP/MCPServerCard.tsx
interface MCPServerCardProps {
  server: MCPServer;
  onConnect: () => void;
  onDisconnect: () => void;
}

const MCPServerCard: React.FC<MCPServerCardProps> = ({
  server,
  onConnect,
  onDisconnect
}) => {
  const isConnected = server.status === 'connected';

  return (
    <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 dark:text-white">
            {server.name}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {server.description}
          </p>
          <div className="flex items-center space-x-2 mt-2">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              isConnected
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
            }`}>
              {server.status}
            </span>
            <span className="text-xs text-gray-500">
              {server.server_type}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {isConnected ? (
            <button
              onClick={onDisconnect}
              className="px-3 py-1 text-sm text-red-600 hover:bg-red-50 rounded"
            >
              断开
            </button>
          ) : (
            <button
              onClick={onConnect}
              className="px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded"
            >
              连接
            </button>
          )}
        </div>
      </div>

      {/* 能力标签 */}
      <div className="flex flex-wrap gap-1 mt-3">
        {server.capabilities.resources && (
          <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
            Resources
          </span>
        )}
        {server.capabilities.tools && (
          <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
            Tools
          </span>
        )}
        {server.capabilities.prompts && (
          <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">
            Prompts
          </span>
        )}
      </div>
    </div>
  );
};
```

### 3.4 模型管理组件

```tsx
// components/Models/ModelList.tsx
const ModelList: React.FC = () => {
  const { models, addModel, updateModel, deleteModel, testModel } = useModelStore();
  const [showAddModal, setShowAddModal] = useState(false);

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            AI模型管理
          </h2>
          <button
            onClick={() => setShowAddModal(true)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
          >
            <PlusIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* 模型列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {models.map((model) => (
          <ModelCard
            key={model.model_id}
            model={model}
            onTest={() => testModel(model.model_id)}
            onEdit={() => {/* 编辑模型 */}}
            onDelete={() => deleteModel(model.model_id)}
          />
        ))}
      </div>

      {/* 添加模型模态框 */}
      <AddModelModal
        open={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={addModel}
      />
    </div>
  );
};
```

```tsx
// components/Models/ModelCard.tsx
interface ModelCardProps {
  model: AIModel;
  onTest: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

const ModelCard: React.FC<ModelCardProps> = ({
  model,
  onTest,
  onEdit,
  onDelete
}) => {
  const [isTestLoading, setIsTestLoading] = useState(false);

  const handleTest = async () => {
    setIsTestLoading(true);
    try {
      await onTest();
    } finally {
      setIsTestLoading(false);
    }
  };

  return (
    <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-gray-900 dark:text-white">
              {model.display_name}
            </h3>
            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
              {model.provider}
            </span>
            {model.is_active ? (
              <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                活跃
              </span>
            ) : (
              <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded">
                停用
              </span>
            )}
          </div>

          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {model.description}
          </p>

          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
            <span>最大Token: {model.max_tokens}</span>
            <span>输入成本: ${model.cost_per_1k_input}/1K</span>
            <span>输出成本: ${model.cost_per_1k_output}/1K</span>
          </div>

          {/* 功能标签 */}
          <div className="flex flex-wrap gap-1 mt-2">
            {model.supports_streaming && (
              <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">
                流式输出
              </span>
            )}
            {model.supports_functions && (
              <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded">
                函数调用
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2 ml-4">
          <button
            onClick={handleTest}
            disabled={isTestLoading}
            className="px-3 py-1 text-sm text-green-600 hover:bg-green-50 rounded disabled:opacity-50"
          >
            {isTestLoading ? '测试中...' : '测试'}
          </button>
          <button
            onClick={onEdit}
            className="px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded"
          >
            编辑
          </button>
          <button
            onClick={onDelete}
            className="px-3 py-1 text-sm text-red-600 hover:bg-red-50 rounded"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  );
};
```

```tsx
// components/Models/ModelConfigForm.tsx
interface ModelConfigFormProps {
  model: AIModel;
  onSave: (config: ModelConfig) => void;
  onCancel: () => void;
}

const ModelConfigForm: React.FC<ModelConfigFormProps> = ({
  model,
  onSave,
  onCancel
}) => {
  const [config, setConfig] = useState<Partial<ModelConfig>>({
    temperature: 0.7,
    max_tokens: 1000,
    top_p: 1.0,
    frequency_penalty: 0.0,
    presence_penalty: 0.0
  });

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">配置 {model.display_name}</h3>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            温度 (Temperature)
          </label>
          <input
            type="number"
            min="0"
            max="2"
            step="0.1"
            value={config.temperature}
            onChange={(e) => setConfig({...config, temperature: parseFloat(e.target.value)})}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            最大Token数
          </label>
          <input
            type="number"
            min="1"
            max={model.max_tokens}
            value={config.max_tokens}
            onChange={(e) => setConfig({...config, max_tokens: parseInt(e.target.value)})}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Top P
          </label>
          <input
            type="number"
            min="0"
            max="1"
            step="0.1"
            value={config.top_p}
            onChange={(e) => setConfig({...config, top_p: parseFloat(e.target.value)})}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            频率惩罚
          </label>
          <input
            type="number"
            min="-2"
            max="2"
            step="0.1"
            value={config.frequency_penalty}
            onChange={(e) => setConfig({...config, frequency_penalty: parseFloat(e.target.value)})}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <button
          onClick={onCancel}
          className="px-4 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded"
        >
          取消
        </button>
        <button
          onClick={() => onSave(config as ModelConfig)}
          className="px-4 py-2 text-sm bg-blue-600 text-white hover:bg-blue-700 rounded"
        >
          保存
        </button>
      </div>
    </div>
  );
};
```

### 3.5 对话组件

```tsx
// components/Chat/ChatArea.tsx
const ChatArea: React.FC = () => {
  const { selectedAgent, currentConversation, sendMessage } = useChatStore();
  const [message, setMessage] = useState('');

  if (!selectedAgent) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <RobotIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
          <p>请选择一个Agent开始对话</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* 对话头部 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
            <RobotIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">
              {selectedAgent.name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {selectedAgent.description}
            </p>
          </div>
        </div>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4">
        <MessageList messages={currentConversation?.messages || []} />
      </div>

      {/* 输入区域 */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <MessageInput
          value={message}
          onChange={setMessage}
          onSend={() => {
            sendMessage(message);
            setMessage('');
          }}
          disabled={selectedAgent.status !== 'running'}
        />
      </div>
    </div>
  );
};
```

## 4. 状态管理

### 4.1 Zustand Store 设计

```tsx
// store/appStore.ts
interface AppState {
  // 用户状态
  user: User | null;
  isAuthenticated: boolean;
  
  // 主题状态
  theme: 'light' | 'dark' | 'system';
  
  // 导航状态
  activeTab: 'agents' | 'knowledge' | 'settings';
  
  // 操作方法
  setUser: (user: User | null) => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  setActiveTab: (tab: string) => void;
}

export const useAppStore = create<AppState>((set) => ({
  user: null,
  isAuthenticated: false,
  theme: 'system',
  activeTab: 'agents',
  
  setUser: (user) => set({ user, isAuthenticated: !!user }),
  setTheme: (theme) => set({ theme }),
  setActiveTab: (activeTab) => set({ activeTab }),
}));
```

```tsx
// store/agentStore.ts
interface AgentState {
  // Agent数据
  agents: Agent[];              // 所有可用的Agent
  loadedAgents: LoadedAgent[];  // 已加载的Agent
  selectedAgent: LoadedAgent | null;
  
  // 操作方法
  discoverAgents: () => Promise<void>;
  loadAgent: (agentId: string) => Promise<void>;
  unloadAgent: (agentId: string) => Promise<void>;
  selectAgent: (agentId: string) => void;
  startAgent: (agentId: string) => Promise<void>;
  stopAgent: (agentId: string) => Promise<void>;
}
```

```tsx
// store/chatStore.ts
interface ChatState {
  // 对话数据
  conversations: Conversation[];
  currentConversation: Conversation | null;

  // WebSocket连接
  socket: Socket | null;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';

  // 操作方法
  createConversation: (agentId: string) => Promise<string>;
  sendMessage: (content: string, type?: string) => Promise<void>;
  connectToAgent: (agentId: string) => Promise<void>;
  disconnectFromAgent: () => void;
}
```

```tsx
// store/mcpStore.ts
interface MCPState {
  // MCP服务器数据
  mcpServers: MCPServer[];
  connectedServers: string[];

  // MCP资源和工具
  serverResources: Record<string, MCPResource[]>;
  serverTools: Record<string, MCPTool[]>;
  serverPrompts: Record<string, MCPPrompt[]>;

  // 操作方法
  discoverServers: () => Promise<void>;
  registerServer: (config: MCPServerConfig) => Promise<string>;
  connectToServer: (serverId: string) => Promise<void>;
  disconnectFromServer: (serverId: string) => Promise<void>;

  // 资源和工具操作
  loadServerResources: (serverId: string) => Promise<void>;
  loadServerTools: (serverId: string) => Promise<void>;
  invokeServerTool: (serverId: string, toolName: string, params: any) => Promise<any>;
  readServerResource: (serverId: string, resourceUri: string) => Promise<string>;
}

export const useMCPStore = create<MCPState>((set, get) => ({
  mcpServers: [],
  connectedServers: [],
  serverResources: {},
  serverTools: {},
  serverPrompts: {},

  discoverServers: async () => {
    try {
      const response = await api.get('/mcp/servers/discover');
      set({ mcpServers: response.data });
    } catch (error) {
      console.error('发现MCP服务器失败:', error);
    }
  },

  registerServer: async (config: MCPServerConfig) => {
    try {
      const response = await api.post('/mcp/servers', config);
      const newServer = response.data;

      set(state => ({
        mcpServers: [...state.mcpServers, newServer]
      }));

      return newServer.server_id;
    } catch (error) {
      console.error('注册MCP服务器失败:', error);
      throw error;
    }
  },

  connectToServer: async (serverId: string) => {
    try {
      await api.post(`/mcp/servers/${serverId}/connect`);

      set(state => ({
        connectedServers: [...state.connectedServers, serverId],
        mcpServers: state.mcpServers.map(server =>
          server.server_id === serverId
            ? { ...server, status: 'connected' }
            : server
        )
      }));

      // 加载服务器资源和工具
      await get().loadServerResources(serverId);
      await get().loadServerTools(serverId);

    } catch (error) {
      console.error('连接MCP服务器失败:', error);
      throw error;
    }
  },

  disconnectFromServer: async (serverId: string) => {
    try {
      await api.post(`/mcp/servers/${serverId}/disconnect`);

      set(state => ({
        connectedServers: state.connectedServers.filter(id => id !== serverId),
        mcpServers: state.mcpServers.map(server =>
          server.server_id === serverId
            ? { ...server, status: 'disconnected' }
            : server
        )
      }));

    } catch (error) {
      console.error('断开MCP服务器失败:', error);
      throw error;
    }
  },

  loadServerResources: async (serverId: string) => {
    try {
      const response = await api.get(`/mcp/servers/${serverId}/resources`);

      set(state => ({
        serverResources: {
          ...state.serverResources,
          [serverId]: response.data
        }
      }));

    } catch (error) {
      console.error('加载服务器资源失败:', error);
    }
  },

  loadServerTools: async (serverId: string) => {
    try {
      const response = await api.get(`/mcp/servers/${serverId}/tools`);

      set(state => ({
        serverTools: {
          ...state.serverTools,
          [serverId]: response.data
        }
      }));

    } catch (error) {
      console.error('加载服务器工具失败:', error);
    }
  },

  invokeServerTool: async (serverId: string, toolName: string, params: any) => {
    try {
      const response = await api.post(
        `/mcp/servers/${serverId}/tools/${toolName}/invoke`,
        { arguments: params }
      );
      return response.data;
    } catch (error) {
      console.error('调用服务器工具失败:', error);
      throw error;
    }
  },

  readServerResource: async (serverId: string, resourceUri: string) => {
    try {
      const response = await api.get(
        `/mcp/servers/${serverId}/resources/read`,
        { params: { uri: resourceUri } }
      );
      return response.data;
    } catch (error) {
      console.error('读取服务器资源失败:', error);
      throw error;
    }
  }
}));
```

## 5. 路由设计

```tsx
// App.tsx
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';

const App: React.FC = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/" element={<ProtectedRoute />}>
          <Route path="" element={<Navigate to="/agents" replace />} />
          <Route path="agents" element={<AgentsPage />} />
          <Route path="knowledge" element={<KnowledgePage />} />
          <Route path="settings" element={<SettingsPage />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
};
```

## 6. WebSocket 集成

```tsx
// hooks/useWebSocket.ts
export const useWebSocket = (agentId: string) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');

  useEffect(() => {
    if (!agentId) return;

    const newSocket = io(`ws://localhost:8000/agents/${agentId}`, {
      auth: {
        token: localStorage.getItem('token')
      }
    });

    newSocket.on('connect', () => {
      setConnectionStatus('connected');
    });

    newSocket.on('disconnect', () => {
      setConnectionStatus('disconnected');
    });

    newSocket.on('message', (data) => {
      // 处理Agent消息
      handleAgentMessage(data);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [agentId]);

  const sendMessage = useCallback((message: any) => {
    if (socket && connectionStatus === 'connected') {
      socket.emit('user_message', message);
    }
  }, [socket, connectionStatus]);

  return { socket, connectionStatus, sendMessage };
};
```

## 7. 主题系统

```tsx
// hooks/useTheme.ts
export const useTheme = () => {
  const { theme } = useAppStore();

  useEffect(() => {
    const root = document.documentElement;
    
    if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [theme]);

  return { theme };
};
```

## 8. 响应式设计

```tsx
// hooks/useResponsive.ts
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setScreenSize('mobile');
      } else if (width < 1024) {
        setScreenSize('tablet');
      } else {
        setScreenSize('desktop');
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return { screenSize, isMobile: screenSize === 'mobile', isTablet: screenSize === 'tablet' };
};
```

## 9. 性能优化

### 9.1 代码分割
```tsx
// 懒加载页面组件
const AgentsPage = lazy(() => import('../pages/AgentsPage'));
const KnowledgePage = lazy(() => import('../pages/KnowledgePage'));
const SettingsPage = lazy(() => import('../pages/SettingsPage'));
```

### 9.2 虚拟滚动
```tsx
// 对于大量数据的列表使用虚拟滚动
import { FixedSizeList as List } from 'react-window';

const VirtualizedMessageList: React.FC<{ messages: Message[] }> = ({ messages }) => {
  return (
    <List
      height={600}
      itemCount={messages.length}
      itemSize={80}
      itemData={messages}
    >
      {MessageItem}
    </List>
  );
};
```

## 10. 测试策略

### 10.1 单元测试
```tsx
// __tests__/components/AgentCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { AgentCard } from '../AgentCard';

describe('AgentCard', () => {
  const mockAgent = {
    id: 'test-agent',
    name: 'Test Agent',
    status: 'running'
  };

  it('renders agent information correctly', () => {
    render(<AgentCard agent={mockAgent} onClick={jest.fn()} />);
    
    expect(screen.getByText('Test Agent')).toBeInTheDocument();
    expect(screen.getByText('running')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<AgentCard agent={mockAgent} onClick={handleClick} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledWith('test-agent');
  });
});
```

### 10.2 集成测试
```tsx
// __tests__/integration/AgentFlow.test.tsx
describe('Agent Flow Integration', () => {
  it('should load and start agent successfully', async () => {
    render(<App />);
    
    // 导航到Agents页面
    fireEvent.click(screen.getByText('Agents'));
    
    // 点击添加Agent
    fireEvent.click(screen.getByText('添加Agent'));
    
    // 选择并加载Agent
    fireEvent.click(screen.getByText('Load Agent'));
    
    // 验证Agent已加载
    await waitFor(() => {
      expect(screen.getByText('Agent loaded successfully')).toBeInTheDocument();
    });
  });
});
```

## 11. 模型管理状态

### 11.1 模型状态管理

```tsx
// store/modelStore.ts
interface ModelState {
  // 模型数据
  models: AIModel[];
  modelConfigs: Record<string, ModelConfig[]>;
  agentModels: Record<string, AgentModel[]>;

  // 使用统计
  modelUsage: Record<string, ModelUsageStats>;

  // 操作方法
  fetchModels: () => Promise<void>;
  addModel: (model: CreateModelRequest) => Promise<string>;
  updateModel: (modelId: string, updates: Partial<AIModel>) => Promise<void>;
  deleteModel: (modelId: string) => Promise<void>;
  testModel: (modelId: string, testMessage?: string) => Promise<ModelTestResult>;

  // API密钥管理
  addApiKey: (modelId: string, keyData: CreateApiKeyRequest) => Promise<void>;
  getApiKeys: (modelId: string) => Promise<ApiKey[]>;
  deleteApiKey: (modelId: string, keyId: string) => Promise<void>;

  // 配置管理
  createModelConfig: (modelId: string, config: CreateConfigRequest) => Promise<string>;
  getModelConfigs: (modelId: string) => Promise<void>;
  updateModelConfig: (configId: string, updates: Partial<ModelConfig>) => Promise<void>;
  deleteModelConfig: (configId: string) => Promise<void>;

  // Agent模型绑定
  bindModelToAgent: (agentId: string, binding: AgentModelBinding) => Promise<void>;
  getAgentModels: (agentId: string) => Promise<void>;
  unbindModelFromAgent: (agentId: string, modelId: string) => Promise<void>;

  // 使用统计
  getModelUsage: (modelId: string, period: string) => Promise<void>;
  getUserModelUsage: (userId: string, period: string) => Promise<ModelUsageStats>;
}

export const useModelStore = create<ModelState>((set, get) => ({
  models: [],
  modelConfigs: {},
  agentModels: {},
  modelUsage: {},

  fetchModels: async () => {
    try {
      const response = await api.get('/models');
      set({ models: response.data });
    } catch (error) {
      console.error('获取模型列表失败:', error);
    }
  },

  addModel: async (model: CreateModelRequest) => {
    try {
      const response = await api.post('/models', model);
      const newModel = response.data;

      set(state => ({
        models: [...state.models, newModel]
      }));

      return newModel.model_id;
    } catch (error) {
      console.error('添加模型失败:', error);
      throw error;
    }
  },

  testModel: async (modelId: string, testMessage = 'Hello, this is a test message.') => {
    try {
      const response = await api.post(`/models/${modelId}/test`, {
        test_message: testMessage
      });
      return response.data;
    } catch (error) {
      console.error('测试模型失败:', error);
      throw error;
    }
  },

  bindModelToAgent: async (agentId: string, binding: AgentModelBinding) => {
    try {
      await api.post(`/agents/${agentId}/models`, binding);
      await get().getAgentModels(agentId);
    } catch (error) {
      console.error('绑定模型到Agent失败:', error);
      throw error;
    }
  },

  getAgentModels: async (agentId: string) => {
    try {
      const response = await api.get(`/agents/${agentId}/models`);

      set(state => ({
        agentModels: {
          ...state.agentModels,
          [agentId]: response.data
        }
      }));
    } catch (error) {
      console.error('获取Agent模型绑定失败:', error);
    }
  }
}));
```

### 11.2 模型类型定义

```tsx
// types/model.ts
export interface AIModel {
  model_id: string;
  model_name: string;
  display_name: string;
  provider: string;
  model_type: 'chat' | 'completion' | 'embedding';
  description: string;
  api_endpoint: string;
  max_tokens: number;
  supports_streaming: boolean;
  supports_functions: boolean;
  cost_per_1k_input: number;
  cost_per_1k_output: number;
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface ModelConfig {
  config_id: string;
  model_id: string;
  user_id?: string;
  config_name: string;
  temperature: number;
  max_tokens: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
  stop_sequences: string[];
  custom_params: Record<string, any>;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface AgentModel {
  agent_model_id: string;
  agent_id: string;
  model_id: string;
  config_id?: string;
  is_primary: boolean;
  priority: number;
  is_active: boolean;
  model: AIModel;
  config?: ModelConfig;
}

export interface ModelUsageStats {
  total_requests: number;
  total_tokens: number;
  total_cost: number;
  average_response_time: number;
  success_rate: number;
  daily_stats: DailyUsageStats[];
}

export interface DailyUsageStats {
  date: string;
  requests: number;
  tokens: number;
  cost: number;
  avg_response_time: number;
}
```
