from sqlalchemy import Column, String, Boolean, DateTime, Text
from app.db.base import Base
import uuid
from datetime import datetime
import json

class Agent(Base):
    __tablename__ = "agents"
    
    agent_id = Column(String(100), primary_key=True)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    version = Column(String(20), nullable=False)
    author = Column(String(100))
    # SQLite不支持ARRAY和JSON类型，存储为字符串
    tags = Column(String)  # 以逗号分隔存储
    config_schema = Column(String)  # 存储为JSON字符串
    file_path = Column(String(500), nullable=False)
    entry_point = Column(String(200), nullable=False)
    requirements = Column(String)  # 以逗号分隔存储
    is_available = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)