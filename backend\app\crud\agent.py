from sqlalchemy.orm import Session
from typing import Optional, List
from app.crud.base import CRUDBase
from app.models.agent import Agent
from app.models.agent_instance import AgentInstance
from app.schemas.agent import AgentCreate, AgentUpdate, AgentInstanceCreate
from datetime import datetime
import uuid

class CRUDAgent(CRUDBase[Agent, AgentCreate, AgentUpdate]):
    def get_by_id(self, db: Session, *, agent_id: str) -> Optional[Agent]:
        return db.query(Agent).filter(Agent.agent_id == agent_id).first()

    def get_available_agents(self, db: Session) -> List[Agent]:
        return db.query(Agent).filter(Agent.is_available == True).all()

    def create_instance(self, db: Session, *, agent_id: str, config: dict = None) -> AgentInstance:
        db_obj = AgentInstance(
            agent_id=agent_id,
            config=config,
            status="loaded",
            health_status="unknown"
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_instance_by_agent_id(self, db: Session, *, agent_id: str) -> Optional[AgentInstance]:
        return db.query(AgentInstance).filter(AgentInstance.agent_id == agent_id).order_by(
            AgentInstance.created_at.desc()
        ).first()

    def get_loaded_instances(self, db: Session) -> List[AgentInstance]:
        return db.query(AgentInstance).filter(AgentInstance.status != "stopped").all()

agent = CRUDAgent(Agent)