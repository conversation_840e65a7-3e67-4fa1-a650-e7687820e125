# AgentsUI 开发环境搭建文档

## 1. 系统要求

### 1.1 硬件要求
- CPU: 4核心以上
- 内存: 8GB以上
- 硬盘: 50GB可用空间
- 网络: 稳定的互联网连接

### 1.2 操作系统支持
- Windows 10/11
- macOS 10.15+
- Ubuntu 20.04+
- 其他Linux发行版

## 2. 基础环境安装

### 2.1 Node.js环境

#### Windows/macOS
```bash
# 使用nvm安装Node.js
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
# 或者使用官网下载器安装

# 安装Node.js 18
nvm install 18
nvm use 18

# 验证安装
node --version  # 应该显示 v18.x.x
npm --version   # 应该显示 9.x.x
```

#### Ubuntu/Debian
```bash
# 使用NodeSource仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 2.2 Python环境

#### 安装Python 3.11
```bash
# Windows (使用Chocolatey)
choco install python311

# macOS (使用Homebrew)
brew install python@3.11

# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev

# 验证安装
python3.11 --version
```

#### 配置虚拟环境
```bash
# 创建虚拟环境
python3.11 -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 2.3 数据库环境

#### PostgreSQL安装
```bash
# Windows (使用Chocolatey)
choco install postgresql

# macOS (使用Homebrew)
brew install postgresql@15
brew services start postgresql@15

# Ubuntu/Debian
sudo apt install postgresql-15 postgresql-contrib-15

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### Redis安装
```bash
# Windows (使用Chocolatey)
choco install redis-64

# macOS (使用Homebrew)
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 2.4 Docker环境

#### Docker Desktop安装
```bash
# Windows/macOS
# 下载并安装Docker Desktop
# https://www.docker.com/products/docker-desktop

# Ubuntu
sudo apt update
sudo apt install apt-transport-https ca-certificates curl software-properties-common
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu focal stable"
sudo apt install docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到docker组
sudo usermod -aG docker $USER
```

#### Docker Compose安装
```bash
# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

## 3. 项目代码获取

### 3.1 克隆仓库
```bash
# 克隆主仓库
git clone https://github.com/your-org/agentsui.git
cd agentsui

# 查看项目结构
tree -L 2
```

### 3.2 项目结构
```
agentsui/
├── frontend/          # React前端项目
├── backend/           # FastAPI后端项目
├── agents/            # Agent代码目录
├── docs/              # 文档目录
├── scripts/           # 脚本目录
├── docker-compose.yml # Docker编排文件
├── .env.example       # 环境变量示例
└── README.md          # 项目说明
```

## 4. 环境配置

### 4.1 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

```bash
# .env文件内容
# 数据库配置
DATABASE_URL=postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI模型配置
OPENAI_API_KEY=sk-your-openai-api-key
DEEPSEEK_API_KEY=***********************************
KIMI_API_KEY=sk-7g3tSy9agawgEu7eHRHGjRsKMUCsgTq0oRKPOhgf2IcoDVex

# 默认模型配置
DEFAULT_MODEL=deepseek
MODEL_FALLBACK_ENABLED=true

# MCP配置
MCP_SERVERS_DIR=./mcp_servers
MCP_TIMEOUT=30

# 开发环境配置
DEBUG=true
LOG_LEVEL=DEBUG
ENVIRONMENT=development

# 前端配置
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
```

### 4.2 数据库初始化
```bash
# 创建数据库
sudo -u postgres createdb agentsui

# 设置postgres用户密码
sudo -u postgres psql -c "ALTER USER postgres PASSWORD '1qaz@wsX';"

# 创建用户（可选）
sudo -u postgres psql -c "CREATE USER agentsui_user WITH PASSWORD '1qaz@wsX';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE agentsui TO agentsui_user;"

# 安装pgvector扩展（用于向量搜索）
sudo -u postgres psql -d agentsui -c "CREATE EXTENSION IF NOT EXISTS vector;"
```

## 5. 依赖安装

### 5.1 后端依赖安装
```bash
cd backend

# 激活虚拟环境
source ../venv/bin/activate  # Linux/macOS
# 或 ..\venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt

# 验证安装
python -c "import fastapi, sqlalchemy, redis; print('后端依赖安装成功')"
```

#### requirements.txt
```txt
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
sqlalchemy>=2.0.0
alembic>=1.12.0
psycopg2-binary>=2.9.0
redis>=5.0.0
pydantic>=2.0.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
websockets>=11.0.0
langchain>=0.1.0
langgraph>=0.0.40
mcp>=1.0.0
```

#### requirements-dev.txt
```txt
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.4.0
```

### 5.2 前端依赖安装
```bash
cd frontend

# 安装依赖
npm install

# 验证安装
npm list react react-dom
```

#### package.json
```json
{
  "name": "agentsui-frontend",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.15.0",
    "typescript": "^5.2.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "tailwindcss": "^3.3.0",
    "zustand": "^4.4.0",
    "axios": "^1.5.0",
    "socket.io-client": "^4.7.0",
    "@tanstack/react-query": "^4.35.0",
    "react-hook-form": "^7.46.0",
    "@headlessui/react": "^1.7.0",
    "@heroicons/react": "^2.0.0"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^4.0.0",
    "vite": "^4.4.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^6.1.0",
    "eslint": "^8.48.0",
    "prettier": "^3.0.0"
  }
}
```

## 6. 开发工具配置

### 6.1 VS Code配置

#### 推荐扩展
```json
// .vscode/extensions.json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.flake8",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode-remote.remote-containers"
  ]
}
```

#### 工作区配置
```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  }
}
```

### 6.2 Git配置

#### Git Hooks配置
```bash
# 安装pre-commit
pip install pre-commit

# 安装hooks
pre-commit install
```

#### .pre-commit-config.yaml
```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.3
    hooks:
      - id: prettier
        files: \.(js|jsx|ts|tsx|json|css|md)$
```

## 7. 启动开发环境

### 7.1 使用Docker Compose启动
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
```

### 7.2 手动启动各服务

#### 启动数据库服务
```bash
# 启动PostgreSQL
sudo systemctl start postgresql

# 启动Redis
sudo systemctl start redis-server
```

#### 启动后端服务
```bash
cd backend
source ../venv/bin/activate

# 运行数据库迁移
alembic upgrade head

# 启动开发服务器
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 启动前端服务
```bash
cd frontend

# 启动开发服务器
npm run dev
```

### 7.3 验证环境
```bash
# 检查后端API
curl http://localhost:8000/health

# 检查前端
curl http://localhost:3000

# 检查数据库连接
PGPASSWORD=1qaz@wsX psql -h 127.0.0.1 -U postgres -d agentsui -c "SELECT version();"

# 检查Redis连接
redis-cli ping
```

## 8. 调试配置

### 8.1 后端调试配置

#### VS Code launch.json
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: FastAPI",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/backend/main.py",
      "console": "integratedTerminal",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend"
      },
      "args": ["--reload", "--host", "0.0.0.0", "--port", "8000"]
    }
  ]
}
```

### 8.2 前端调试配置

#### Chrome调试
```json
{
  "name": "Chrome: React",
  "type": "chrome",
  "request": "launch",
  "url": "http://localhost:3000",
  "webRoot": "${workspaceFolder}/frontend/src"
}
```

## 9. 测试环境

### 9.1 运行测试
```bash
# 后端测试
cd backend
pytest tests/ -v --cov=app

# 前端测试
cd frontend
npm test

# E2E测试
npm run test:e2e
```

### 9.2 测试数据库
```bash
# 创建测试数据库
sudo -u postgres createdb agentsui_test

# 运行测试迁移
DATABASE_URL=postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui_test alembic upgrade head
```

## 10. 常见问题解决

### 10.1 端口冲突
```bash
# 查看端口占用
lsof -i :8000
netstat -tulpn | grep :8000

# 杀死进程
kill -9 <PID>
```

### 10.2 权限问题
```bash
# 修复文件权限
chmod +x scripts/*.sh

# 修复Docker权限
sudo usermod -aG docker $USER
newgrp docker
```

### 10.3 依赖问题
```bash
# 清理npm缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 清理pip缓存
pip cache purge
pip install --force-reinstall -r requirements.txt
```

### 10.4 数据库连接问题
```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 重启PostgreSQL
sudo systemctl restart postgresql

# 检查连接配置
sudo -u postgres psql -c "\conninfo"
```

## 11. 开发工作流

### 11.1 日常开发流程
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 创建功能分支
git checkout -b feature/new-feature

# 3. 启动开发环境
docker-compose up -d

# 4. 进行开发
# ...

# 5. 运行测试
npm test
pytest

# 6. 提交代码
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature

# 7. 创建Pull Request
```

### 11.2 代码规范
```bash
# 格式化代码
black backend/
prettier --write frontend/src/

# 检查代码质量
flake8 backend/
npm run lint

# 类型检查
mypy backend/
npm run type-check
```
