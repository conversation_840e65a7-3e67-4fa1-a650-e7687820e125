from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app import schemas, models, crud
from app.db.session import get_db
from app.schemas.base import SuccessResponse, PaginatedResponse, PaginationBase

router = APIRouter()

@router.get("/", response_model=SuccessResponse[List[schemas.ai_model.AIModel]])
def get_models(
    db: Session = Depends(get_db)
):
    """
    获取可用模型列表
    """
    models = crud.ai_model.get_active_models(db)
    return SuccessResponse(
        data=[schemas.ai_model.AIModel.from_orm(model) for model in models]
    )

@router.post("/", response_model=SuccessResponse[schemas.ai_model.AIModel])
def create_model(
    model_in: schemas.ai_model.AIModelCreate,
    db: Session = Depends(get_db)
):
    """
    添加新模型
    """
    # 检查模型名称是否已存在
    model = crud.ai_model.get_by_name(db, model_name=model_in.model_name)
    if model:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="模型名称已存在"
        )
    
    # 创建模型
    model = crud.ai_model.create(db, obj_in=model_in)
    
    return SuccessResponse(
        data=schemas.ai_model.AIModel.from_orm(model)
    )

@router.put("/{model_id}", response_model=SuccessResponse[schemas.ai_model.AIModel])
def update_model(
    model_id: str,
    model_in: schemas.ai_model.AIModelUpdate,
    db: Session = Depends(get_db)
):
    """
    更新模型配置
    """
    model = crud.ai_model.get(db, id=model_id)
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )
    
    # 更新模型
    model = crud.ai_model.update(db, db_obj=model, obj_in=model_in)
    
    return SuccessResponse(
        data=schemas.ai_model.AIModel.from_orm(model)
    )

@router.delete("/{model_id}", response_model=SuccessResponse[bool])
def delete_model(
    model_id: str,
    db: Session = Depends(get_db)
):
    """
    删除模型
    """
    model = crud.ai_model.get(db, id=model_id)
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )
    
    crud.ai_model.remove(db, id=model_id)
    
    return SuccessResponse(
        data=True,
        message="模型删除成功"
    )