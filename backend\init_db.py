from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.db.base import Base
from app.models.user import User
from app.models.agent import Agent
from app.models.conversation import Conversation
from app.models.message import Message
from app.models.ai_model import AIModel
from app.core.config import settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_db():
    engine = create_engine(
        settings.SQLALCHEMY_DATABASE_URI,
        connect_args={"check_same_thread": False}
    )
    
    # 删除所有表
    Base.metadata.drop_all(bind=engine)
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    # 检查是否已存在用户
    user_count = db.query(User).count()
    if user_count == 0:
        logger.info("Creating initial user")
        # 创建初始用户
        initial_user = User(
            username="admin",
            email="<EMAIL>",
            is_active=True
        )
        # 注意：在生产环境中，应该使用加密密码
        initial_user.password_hash = "admin123"
        db.add(initial_user)
        db.commit()
        logger.info("Initial user created")
    else:
        logger.info("Users already exist, skipping initial user creation")
    
    db.close()
    logger.info("Database initialized successfully")

if __name__ == "__main__":
    init_db()