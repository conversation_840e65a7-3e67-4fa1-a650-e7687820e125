# AgentsUI 项目需求文档

## 1. 项目概述

### 1.1 项目背景
基于LangChain、LangGraph、AG-UI协议开发的多Agent管理和交互平台。项目中已有多个Agent存放在Agents/文件夹下，每个Agent都是一个独立的文件夹。现需要开发一个统一的Web界面来管理和使用这些Agent，同时支持Model Context Protocol (MCP)集成。

### 1.2 项目目标
- 提供统一的Agent管理和交互界面
- 支持多用户、多Agent并发使用
- 集成MCP协议，扩展Agent能力
- 提供完整的知识库管理功能
- 确保系统安全性和可扩展性

### 1.3 目标用户
- AI开发者和研究人员
- 企业用户和团队
- 需要使用多个AI Agent的业务场景
- 需要集成第三方服务的用户

## 2. 功能需求

### 2.1 用户界面布局

#### 2.1.1 整体布局结构
采用顶部菜单 + 四列主区域的布局设计：

**顶部菜单区域**（从左至右）：
- 项目Logo和品牌标识
- 当前导航路径 + 页面标题（面包屑导航）
- 主题切换器（随系统、浅色模式、深色模式）
- 用户信息和账户管理（右对齐）

**四列主区域**（从左至右）：
1. **导航栏**：竖向排列，icon+文字方式，支持左右缩放
   - Agents（智能体管理）
   - 知识库（Knowledge Base）
   - 设置（Settings）

2. **列表区域**：根据导航选择显示不同内容
   - 选中Agents：显示Agent列表
   - 选中知识库：显示知识库分类
   - 选中设置：显示设置分类

3. **主内容区域**：
   - 选中Agents：Agent对话交互区
   - 选中知识库：知识库内容列表
   - 选中设置：具体设置项配置

4. **工具面板**：
   - 仅在Agents选中时显示
   - 显示当前Agent的专用工具
   - 根据不同Agent动态变化内容

#### 2.1.2 响应式设计要求
- 支持桌面端（1920x1080及以上）
- 支持平板端（768px-1024px）
- 支持移动端（320px-768px）
- 在小屏幕上自动折叠侧边栏

### 2.2 Agent管理功能

#### 2.2.1 Agent发现和加载
- **自动发现**：扫描Agents/文件夹下的所有Agent
- **状态显示**：显示Agent的加载状态（未加载、已加载、运行中、错误）
- **批量操作**：支持批量加载、启动、停止Agent
- **配置管理**：为每个Agent提供独立的配置界面

#### 2.2.2 Agent生命周期管理
- **加载Agent**：读取Agent配置，验证依赖，加载到系统
- **启动Agent**：启动Agent进程，建立通信连接
- **监控Agent**：实时监控Agent运行状态和性能指标
- **停止Agent**：安全停止Agent进程，清理资源
- **卸载Agent**：从系统中移除Agent，释放内存

#### 2.2.3 Agent配置管理
- **配置界面**：为每个Agent提供图形化配置界面
- **参数验证**：根据Agent的config_schema验证配置参数
- **配置保存**：支持配置的保存、导入、导出
- **版本管理**：支持Agent配置的版本控制和回滚

### 2.3 对话交互功能

#### 2.3.1 实时对话
- **WebSocket通信**：基于WebSocket的实时双向通信
- **消息类型**：支持文本、图片、文件、结构化数据等多种消息类型
- **流式响应**：支持Agent的流式输出和实时显示
- **消息历史**：完整的对话历史记录和搜索功能

#### 2.3.2 多会话管理
- **会话创建**：为每个Agent创建独立的对话会话
- **会话切换**：支持在多个会话间快速切换
- **会话持久化**：会话数据的自动保存和恢复
- **会话分享**：支持会话的导出和分享功能

#### 2.3.3 交互增强
- **快捷操作**：常用操作的快捷键支持
- **消息编辑**：支持消息的编辑和重新发送
- **上下文管理**：智能的上下文管理和清理
- **中断控制**：支持长时间运行任务的中断和恢复

### 2.4 知识库管理功能

#### 2.4.1 知识库组织
- **分类管理**：支持知识库的分类和标签管理
- **层级结构**：支持多层级的知识库组织结构
- **权限控制**：不同用户对知识库的访问权限控制
- **搜索功能**：全文搜索和语义搜索功能

#### 2.4.2 文档管理
- **文档上传**：支持多种格式文档的上传（PDF、Word、TXT、Markdown等）
- **文档解析**：自动解析文档内容，提取关键信息
- **版本控制**：文档的版本管理和变更追踪
- **批量操作**：支持文档的批量导入、导出、删除

#### 2.4.3 向量化处理
- **自动向量化**：文档内容的自动向量化处理
- **向量搜索**：基于向量相似度的智能搜索
- **模型选择**：支持多种embedding模型的选择
- **索引优化**：向量索引的优化和管理

### 2.5 用户管理功能

#### 2.5.1 用户认证
- **注册登录**：用户注册、登录、密码重置功能
- **多因素认证**：支持2FA等多因素认证方式
- **单点登录**：支持LDAP、OAuth等单点登录集成
- **会话管理**：用户会话的管理和安全控制

#### 2.5.2 权限管理
- **角色定义**：管理员、普通用户、访客等角色定义
- **权限分配**：细粒度的功能权限分配
- **资源访问**：对Agent、知识库等资源的访问控制
- **审计日志**：用户操作的完整审计日志

#### 2.5.3 用户体验
- **个人设置**：用户个人偏好和设置管理
- **使用统计**：用户使用情况的统计和分析
- **通知系统**：系统通知和消息推送功能
- **帮助文档**：内置的帮助文档和使用指南

### 2.6 模型管理功能

#### 2.6.1 模型配置
- **模型注册**：支持添加、编辑、删除AI模型配置
- **API密钥管理**：安全存储和管理各模型的API密钥
- **模型参数**：配置模型的基础参数（温度、最大token等）
- **模型测试**：提供模型连接测试和响应验证功能

#### 2.6.2 预设模型
- **DeepSeek模型**：
  - 模型名称：deepseek
  - API密钥：***********************************
  - 支持对话和代码生成
- **Kimi模型**：
  - 模型名称：Kimi-k2
  - API密钥：sk-7g3tSy9agawgEu7eHRHGjRsKMUCsgTq0oRKPOhgf2IcoDVex
  - 支持长文本处理和多模态交互

#### 2.6.3 模型分配
- **Agent绑定**：为每个Agent指定使用的模型
- **动态切换**：支持Agent运行时切换模型
- **负载均衡**：多个相同模型间的负载分配
- **故障转移**：主模型不可用时的备用模型切换

#### 2.6.4 使用监控
- **调用统计**：记录各模型的调用次数和使用时长
- **成本分析**：基于API调用的成本统计和分析
- **性能监控**：模型响应时间和成功率监控
- **配额管理**：API调用配额的管理和告警

## 3. MCP集成需求

### 3.1 MCP服务器管理

#### 3.1.1 服务器配置
- **添加服务器**：用户可在设置页面添加第三方MCP服务器
- **连接方式**：支持stdio、HTTP/WebSocket、Docker容器等多种连接方式
- **配置验证**：自动验证MCP服务器配置的正确性
- **批量管理**：支持MCP服务器的批量添加和管理

#### 3.1.2 连接管理
- **状态监控**：实时显示MCP服务器的连接状态和健康状况
- **自动重连**：连接断开时的自动重连机制
- **负载均衡**：多个MCP服务器间的负载均衡
- **故障转移**：服务器故障时的自动故障转移

#### 3.1.3 生命周期管理
- **启动停止**：MCP服务器的启动、停止、重启操作
- **版本管理**：MCP服务器版本的管理和升级
- **配置更新**：运行时配置的动态更新
- **资源清理**：服务器停止时的资源清理

### 3.2 MCP功能集成

#### 3.2.1 资源访问（Resources）
- **资源发现**：自动发现MCP服务器提供的资源
- **资源类型**：支持文件系统、数据库、API等各种资源类型
- **访问控制**：对MCP资源的访问权限控制
- **缓存机制**：资源访问的缓存和优化

#### 3.2.2 工具调用（Tools）
- **工具发现**：自动发现和注册MCP服务器提供的工具
- **参数验证**：工具调用参数的验证和类型检查
- **结果处理**：工具调用结果的处理和格式化
- **错误处理**：工具调用失败时的错误处理和重试

#### 3.2.3 提示词模板（Prompts）
- **模板管理**：MCP提示词模板的管理和组织
- **模板应用**：在Agent对话中应用MCP提示词模板
- **参数替换**：模板参数的动态替换和填充
- **版本控制**：提示词模板的版本管理

### 3.3 MCP安全控制

#### 3.3.1 权限管理
- **访问控制**：对MCP服务器访问的细粒度权限控制
- **用户授权**：用户对特定MCP功能的授权管理
- **操作审计**：MCP操作的完整审计日志
- **权限继承**：权限的继承和委托机制

#### 3.3.2 安全策略
- **沙箱模式**：MCP服务器的沙箱隔离运行
- **访问限制**：限制MCP服务器的文件系统和网络访问
- **资源配额**：MCP服务器的资源使用配额限制
- **安全扫描**：MCP服务器和工具的安全扫描

#### 3.3.3 用户确认机制
- **操作确认**：敏感操作的用户确认机制
- **风险提示**：高风险操作的风险提示和警告
- **操作记录**：用户确认操作的完整记录
- **撤销机制**：已执行操作的撤销和回滚

## 4. 技术需求

### 4.1 性能要求
- **响应时间**：页面加载时间 < 2秒，API响应时间 < 500ms
- **并发支持**：支持100+用户同时在线使用
- **资源占用**：合理的CPU和内存资源占用
- **扩展性**：支持水平扩展和负载均衡

### 4.2 兼容性要求
- **浏览器支持**：Chrome 90+、Firefox 88+、Safari 14+、Edge 90+
- **操作系统**：Windows 10+、macOS 10.15+、Ubuntu 20.04+
- **移动端**：iOS 14+、Android 10+
- **网络环境**：支持IPv4/IPv6，适应不同网络环境

### 4.3 安全要求
- **数据加密**：传输和存储数据的加密保护
- **访问控制**：完善的身份认证和授权机制
- **安全审计**：完整的安全审计日志
- **漏洞防护**：常见Web安全漏洞的防护

### 4.4 可维护性要求
- **代码质量**：高质量、可维护的代码结构
- **文档完整**：完整的技术文档和用户文档
- **测试覆盖**：充分的单元测试和集成测试
- **监控告警**：完善的系统监控和告警机制

## 5. 部署要求

### 5.1 部署方式
- **容器化部署**：基于Docker的容器化部署
- **云原生支持**：支持Kubernetes等云原生平台
- **多环境支持**：开发、测试、生产环境的支持
- **自动化部署**：CI/CD自动化部署流程

### 5.2 运维要求
- **监控体系**：完整的系统监控和性能监控
- **日志管理**：集中化的日志收集和分析
- **备份恢复**：数据备份和灾难恢复机制
- **升级维护**：平滑的系统升级和维护流程

## 6. 验收标准

### 6.1 功能验收
- 所有核心功能正常运行
- MCP集成功能完整可用
- 用户界面友好易用
- 性能指标达到要求

### 6.2 质量验收
- 代码质量符合规范
- 测试覆盖率 > 80%
- 安全测试通过
- 文档完整准确

### 6.3 部署验收
- 部署流程自动化
- 监控告警正常
- 备份恢复可用
- 运维文档完整
