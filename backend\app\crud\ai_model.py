from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Optional, List
from app.crud.base import CRUDBase
from app.models.ai_model import AIModel
from app.schemas.ai_model import AIModelCreate, AIModelUpdate

class CRUDAIModel(CRUDBase[AIModel, AIModelCreate, AIModelUpdate]):
    def get_by_name(self, db: Session, *, model_name: str) -> Optional[AIModel]:
        return db.query(AIModel).filter(AIModel.model_name == model_name).first()

    def get_active_models(self, db: Session) -> List[AIModel]:
        return db.query(AIModel).filter(AIModel.is_active == True).all()

ai_model = CRUDAIModel(AIModel)