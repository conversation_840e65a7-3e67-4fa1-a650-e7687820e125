# AgentsUI 数据库配置说明

## 1. 数据库连接信息

### 1.1 开发环境数据库配置
```
主机地址：127.0.0.1
端口：5432
用户名：postgres
密码：1qaz@wsX
数据库名：agentsui
```

### 1.2 连接字符串格式
```
# 标准连接字符串
DATABASE_URL=postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui

# 测试数据库连接字符串
DATABASE_URL_TEST=postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui_test
```

## 2. 数据库初始化

### 2.1 创建数据库
```bash
# 连接到PostgreSQL
PGPASSWORD=1qaz@wsX psql -h 127.0.0.1 -U postgres

# 创建主数据库
CREATE DATABASE agentsui;

# 创建测试数据库
CREATE DATABASE agentsui_test;

# 安装pgvector扩展（用于向量搜索）
\c agentsui
CREATE EXTENSION IF NOT EXISTS vector;

\c agentsui_test
CREATE EXTENSION IF NOT EXISTS vector;
```

### 2.2 用户权限配置
```sql
-- 为postgres用户设置密码
ALTER USER postgres PASSWORD '1qaz@wsX';

-- 创建专用应用用户（可选）
CREATE USER agentsui_user WITH PASSWORD '1qaz@wsX';
GRANT ALL PRIVILEGES ON DATABASE agentsui TO agentsui_user;
GRANT ALL PRIVILEGES ON DATABASE agentsui_test TO agentsui_user;
```

## 3. 环境变量配置

### 3.1 开发环境 (.env)
```bash
# 数据库配置
DATABASE_URL=postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui
DATABASE_URL_TEST=postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui_test

# Redis配置
REDIS_URL=redis://localhost:6379

# 应用配置
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# OpenAI配置
OPENAI_API_KEY=sk-your-openai-api-key

# MCP配置
MCP_SERVERS_DIR=./mcp_servers
MCP_TIMEOUT=30

# 调试配置
DEBUG=true
LOG_LEVEL=DEBUG
ENVIRONMENT=development
```

### 3.2 生产环境配置
```bash
# 数据库配置（生产环境应使用更安全的密码）
DATABASE_URL=*******************************************************/agentsui

# Redis配置
REDIS_URL=redis://redis_host:6379

# 应用配置
JWT_SECRET_KEY=${JWT_SECRET_KEY}
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=production
```

## 4. Docker环境配置

### 4.1 Docker Compose配置
```yaml
# docker-compose.yml
services:
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=agentsui
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1qaz@wsX
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"

  backend:
    build: ./backend
    environment:
      - DATABASE_URL=********************************************/agentsui
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
```

### 4.2 初始化脚本 (scripts/init.sql)
```sql
-- 创建扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建应用用户
CREATE USER agentsui_user WITH PASSWORD '1qaz@wsX';
GRANT ALL PRIVILEGES ON DATABASE agentsui TO agentsui_user;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO agentsui_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO agentsui_user;
```

## 5. 数据库迁移

### 5.1 Alembic配置
```python
# alembic.ini
[alembic]
sqlalchemy.url = postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui
```

### 5.2 迁移命令
```bash
# 初始化迁移
alembic init alembic

# 创建迁移文件
alembic revision --autogenerate -m "Initial migration"

# 执行迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

## 6. 连接测试

### 6.1 命令行测试
```bash
# 使用psql连接
PGPASSWORD=1qaz@wsX psql -h 127.0.0.1 -U postgres -d agentsui

# 测试连接
\conninfo

# 查看数据库列表
\l

# 查看表列表
\dt
```

### 6.2 Python代码测试
```python
import asyncpg
import asyncio

async def test_connection():
    try:
        conn = await asyncpg.connect(
            host='127.0.0.1',
            port=5432,
            user='postgres',
            password='1qaz@wsX',
            database='agentsui'
        )
        
        version = await conn.fetchval('SELECT version()')
        print(f"连接成功: {version}")
        
        await conn.close()
        
    except Exception as e:
        print(f"连接失败: {e}")

# 运行测试
asyncio.run(test_connection())
```

## 7. 安全注意事项

### 7.1 密码安全
- 开发环境密码：`1qaz@wsX`
- 生产环境应使用更复杂的密码
- 定期更换数据库密码
- 使用环境变量存储敏感信息

### 7.2 网络安全
- 限制数据库访问IP范围
- 使用SSL连接（生产环境）
- 配置防火墙规则
- 定期更新PostgreSQL版本

### 7.3 备份策略
```bash
# 数据库备份
PGPASSWORD=1qaz@wsX pg_dump -h 127.0.0.1 -U postgres agentsui > backup_$(date +%Y%m%d).sql

# 数据库恢复
PGPASSWORD=1qaz@wsX psql -h 127.0.0.1 -U postgres agentsui < backup_20240101.sql
```

## 8. 故障排除

### 8.1 常见问题
1. **连接被拒绝**
   - 检查PostgreSQL服务是否启动
   - 检查端口5432是否开放
   - 验证用户名和密码

2. **权限不足**
   - 检查用户权限设置
   - 确认数据库存在
   - 验证用户对数据库的访问权限

3. **编码问题**
   - 确保数据库编码为UTF-8
   - 检查客户端编码设置

### 8.2 调试命令
```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 查看PostgreSQL日志
sudo tail -f /var/log/postgresql/postgresql-15-main.log

# 检查端口占用
netstat -tulpn | grep :5432

# 测试网络连接
telnet 127.0.0.1 5432
```

## 9. 性能优化

### 9.1 连接池配置
```python
# SQLAlchemy连接池配置
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    "postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui",
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

### 9.2 PostgreSQL优化
```sql
-- postgresql.conf 优化配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
max_connections = 100
```

## 10. 监控配置

### 10.1 连接监控
```python
# 数据库连接监控
import psutil
import asyncpg

async def monitor_db_connections():
    conn = await asyncpg.connect(
        "postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui"
    )
    
    # 查询活跃连接数
    active_connections = await conn.fetchval(
        "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
    )
    
    print(f"活跃连接数: {active_connections}")
    await conn.close()
```

### 10.2 性能监控
```sql
-- 查询慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 查询数据库大小
SELECT pg_size_pretty(pg_database_size('agentsui'));

-- 查询表大小
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```
