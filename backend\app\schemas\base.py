from typing import Generic, TypeVar, Optional, Any
from pydantic import BaseModel, model_validator, Field, field_validator
from datetime import datetime

T = TypeVar('T')

class ResponseBase(BaseModel):
    success: bool = Field(default=True)
    message: Optional[str] = Field(default="操作成功")
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class SuccessResponse(ResponseBase, Generic[T]):
    data: T

class ErrorResponse(ResponseBase):
    success: bool = False
    message: str = "操作失败"
    error: Optional[dict] = None

class PaginationBase(BaseModel):
    page: int = 1
    size: int = 20
    total: int = 0
    pages: int = 0

class PaginatedResponse(ResponseBase, Generic[T]):
    data: dict = Field(default_factory=dict)

    