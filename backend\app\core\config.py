from typing import List, Union
from pydantic_settings import BaseSettings
from pydantic import AnyHttpUrl

class Settings(BaseSettings):
    PROJECT_NAME: str = "AgentsUI"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = "your-secret-key-here"  # 在生产环境中应该从环境变量获取
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # 数据库配置 - 使用SQLite进行测试
    SQLALCHEMY_DATABASE_URI: str = "sqlite:///./test.db"
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["*"]  # 在生产环境中应该指定具体的域名
    
    class Config:
        case_sensitive = True
        env_file = ".env"

settings = Settings()