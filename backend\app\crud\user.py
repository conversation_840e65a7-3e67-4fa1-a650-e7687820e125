from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Optional, List
from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from datetime import datetime
import hashlib
import secrets
import jwt
import time
from app.core.config import settings

class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    def get_by_username(self, db: Session, *, username: str) -> Optional[User]:
        return db.query(User).filter(func.lower(User.username) == func.lower(username)).first()

    def get_by_email(self, db: Session, *, email: str) -> Optional[User]:
        return db.query(User).filter(func.lower(User.email) == func.lower(email)).first()

    def create(self, db: Session, *, obj_in: UserCreate) -> User:
        # 对密码进行哈希处理
        password_hash = self.get_password_hash(obj_in.password)
        
        db_obj = User(
            username=obj_in.username,
            email=obj_in.email,
            password_hash=password_hash,
            full_name=obj_in.full_name,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_last_login(self, db: Session, *, user_id: str) -> User:
        user = db.query(User).filter(User.user_id == user_id).first()
        if user:
            user.last_login_at = datetime.utcnow()
            db.add(user)
            db.commit()
            db.refresh(user)
        return user

    def authenticate(self, db: Session, *, username: str, password: str) -> Optional[User]:
        user = self.get_by_username(db, username=username)
        if not user:
            return None
        if not self.verify_password(password, user.password_hash):
            return None
        return user

    def get_password_hash(self, password: str) -> str:
        # 简单的密码哈希实现（在生产环境中应使用更安全的方法，如bcrypt）
        salt = secrets.token_hex(16)
        pwdhash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
        return salt + pwdhash.hex()

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        # 验证密码
        salt = hashed_password[:32]  # 前32个字符是salt
        stored_password = hashed_password[32:]
        pwdhash = hashlib.pbkdf2_hmac('sha256',
                                      plain_password.encode('utf-8'),
                                      salt.encode('utf-8'),
                                      100000)
        return pwdhash.hex() == stored_password

    def generate_access_token(self, user_id: str) -> str:
        # 生成访问令牌（简化实现，实际应使用JWT）
        try:
            payload = {
                "sub": str(user_id),
                "exp": int(time.time()) + settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            }
            return jwt.encode(payload, settings.SECRET_KEY, algorithm="HS256")
        except Exception:
            # 简化实现，返回模拟token
            return f"access_token_for_{user_id}"

    def generate_refresh_token(self, user_id: str) -> str:
        # 生成刷新令牌（简化实现）
        try:
            payload = {
                "sub": str(user_id),
                "exp": int(time.time()) + settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
            }
            return jwt.encode(payload, settings.SECRET_KEY, algorithm="HS256")
        except Exception:
            # 简化实现，返回模拟token
            return f"refresh_token_for_{user_id}"

user = CRUDUser(User)