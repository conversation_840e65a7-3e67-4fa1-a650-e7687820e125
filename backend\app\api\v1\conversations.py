from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from typing import List
from app import schemas, models, crud
from app.db.session import get_db
from app.schemas.base import SuccessResponse, PaginatedResponse, PaginationBase
import logging
import traceback

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/", response_model=SuccessResponse[schemas.conversation.Conversation])
def create_conversation(
    conversation_in: schemas.conversation.ConversationCreate,
    db: Session = Depends(get_db)
):
    """
    创建对话会话
    """
    try:
        # 创建对话
        conversation = crud.conversation.create(db, obj_in=conversation_in)
        
        return SuccessResponse(
            data=schemas.conversation.Conversation.from_orm(conversation)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_conversation: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/", response_model=PaginatedResponse[List[schemas.conversation.Conversation]])
def get_conversations(
    page: int = 1,
    size: int = 20,
    agent_id: str = None,
    db: Session = Depends(get_db)
):
    """
    获取对话列表
    """
    try:
        conversations, total = crud.conversation.get_multi(
            db, page=page, size=size, agent_id=agent_id
        )
        
        pagination = PaginationBase(
            page=page,
            size=size,
            total=total,
            pages=(total + size - 1) // size
        )
        
        response = PaginatedResponse()
        response.data = {
            "items": [schemas.conversation.Conversation.from_orm(conv) for conv in conversations],
            "pagination": pagination
        }
        
        return response
    except Exception as e:
        logger.error(f"Error getting conversations: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话列表失败"
        )

@router.get("/{conversation_id}", response_model=SuccessResponse[schemas.conversation.Conversation])
def get_conversation(
    conversation_id: str,
    db: Session = Depends(get_db)
):
    """
    获取对话详情
    """
    try:
        conversation = crud.conversation.get(db, id=conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        return SuccessResponse(
            data=schemas.conversation.Conversation.from_orm(conversation)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取对话详情失败"
        )

@router.delete("/{conversation_id}", response_model=SuccessResponse[bool])
def delete_conversation(
    conversation_id: str,
    db: Session = Depends(get_db)
):
    """
    删除对话
    """
    try:
        conversation = crud.conversation.get(db, id=conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在"
            )
        
        crud.conversation.remove(db, id=conversation_id)
        
        return SuccessResponse(
            data=True,
            message="对话删除成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting conversation: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除对话失败"
        )