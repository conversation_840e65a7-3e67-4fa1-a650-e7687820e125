from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app import schemas, models, crud
from app.db.session import get_db
from app.schemas.base import SuccessResponse, ErrorResponse
import uuid

router = APIRouter()

@router.post("/register", response_model=SuccessResponse[schemas.user.User])
def register_user(
    user_in: schemas.user.UserCreate,
    db: Session = Depends(get_db)
):
    """
    用户注册
    """
    # 检查用户名是否已存在
    user = crud.user.get_by_username(db, username=user_in.username)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    user = crud.user.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )
    
    # 创建用户
    user = crud.user.create(db, obj_in=user_in)
    
    return SuccessResponse(
        data=schemas.user.User.from_orm(user),
        message="用户注册成功"
    )

@router.post("/login", response_model=SuccessResponse[schemas.user.Token])
def login_user(
    user_in: schemas.user.UserLogin,
    db: Session = Depends(get_db)
):
    """
    用户登录
    """
    user = crud.user.authenticate(
        db, username=user_in.username, password=user_in.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名或密码错误"
        )
    
    # 生成访问令牌和刷新令牌
    access_token = crud.user.generate_access_token(user.user_id)
    refresh_token = crud.user.generate_refresh_token(user.user_id)
    
    # 更新最后登录时间
    crud.user.update_last_login(db, user_id=user.user_id)
    
    return SuccessResponse(
        data=schemas.user.Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=3600,
            user=schemas.user.User.from_orm(user)
        ),
        message="登录成功"
    )