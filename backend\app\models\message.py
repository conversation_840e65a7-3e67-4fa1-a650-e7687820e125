from sqlalchemy import Column, String, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.dialects.sqlite import VARCHAR
from app.db.base import Base
import uuid
from datetime import datetime

class Message(Base):
    __tablename__ = "messages"
    
    # 使用VARCHAR代替UUID以兼容SQLite
    message_id = Column(VARCHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(VARCHAR(36), Foreign<PERSON>ey("conversations.conversation_id"), nullable=False)
    sender = Column(String(20), nullable=False)  # user, agent, system
    content = Column(Text, nullable=False)
    message_type = Column(String(50), default="text")  # text, image, file, json
    # SQLite不支持JSON类型，存储为字符串
    data = Column(Text)  # 存储为JSON字符串
    parent_message_id = Column(VARCHAR(36), ForeignKey("messages.message_id"))
    is_edited = Column(<PERSON><PERSON><PERSON>, default=False)
    edit_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)