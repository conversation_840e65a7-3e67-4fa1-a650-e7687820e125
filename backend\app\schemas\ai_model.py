from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from decimal import Decimal
from uuid import UUID

class AIModelBase(BaseModel):
    model_name: str = Field(..., max_length=100)
    display_name: str = Field(..., max_length=200)
    provider: str = Field(..., max_length=50)
    model_type: str = "chat"
    description: Optional[str] = None
    api_endpoint: Optional[str] = Field(None, max_length=500)
    max_tokens: int = 4096
    supports_streaming: bool = True
    supports_functions: bool = False
    cost_per_1k_input: Decimal = Decimal('0')
    cost_per_1k_output: Decimal = Decimal('0')

class AIModelCreate(AIModelBase):
    pass

class AIModelUpdate(AIModelBase):
    model_name: Optional[str] = Field(None, max_length=100)
    display_name: Optional[str] = Field(None, max_length=200)
    provider: Optional[str] = Field(None, max_length=50)
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None

class AIModel(AIModelBase):
    model_id: UUID
    is_active: bool = True
    is_default: bool = False
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ModelConfigBase(BaseModel):
    config_name: str = Field(..., max_length=100)
    temperature: Decimal = Decimal('0.7')
    max_tokens: int = 1000
    top_p: Decimal = Decimal('1.0')
    frequency_penalty: Decimal = Decimal('0.0')
    presence_penalty: Decimal = Decimal('0.0')
    stop_sequences: Optional[List[str]] = None
    custom_params: Optional[Dict[str, Any]] = None

class ModelConfigCreate(ModelConfigBase):
    model_id: UUID

class ModelConfigUpdate(ModelConfigBase):
    config_name: Optional[str] = Field(None, max_length=100)

class ModelConfig(ModelConfigBase):
    config_id: UUID
    model_id: UUID
    user_id: Optional[UUID] = None
    is_default: bool = False
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ModelApiKeyBase(BaseModel):
    key_name: str = Field(..., max_length=100)
    api_key: str = Field(..., min_length=1)
    expires_at: Optional[datetime] = None

class ModelApiKeyCreate(ModelApiKeyBase):
    pass

class ModelApiKeyUpdate(BaseModel):
    key_name: Optional[str] = Field(None, max_length=100)
    is_active: Optional[bool] = None

class ModelApiKey(ModelApiKeyBase):
    key_id: UUID
    model_id: UUID
    user_id: Optional[UUID] = None
    api_key_masked: str
    is_active: bool = True
    last_used_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True