from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app import schemas, models, crud
from app.db.session import get_db
from app.schemas.base import SuccessResponse

router = APIRouter()

@router.post("/", response_model=SuccessResponse[schemas.agent.Agent])
def create_agent(
    agent_in: schemas.agent.AgentCreate,
    db: Session = Depends(get_db)
):
    """
    创建新的Agent
    """
    # 检查Agent是否已存在
    agent = crud.agent.get_by_id(db, agent_id=agent_in.agent_id)
    if agent:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Agent已存在"
        )
    
    # 创建Agent
    agent = crud.agent.create(db, obj_in=agent_in)
    
    return SuccessResponse(
        data=schemas.agent.Agent.from_orm(agent),
        message="Agent创建成功"
    )

@router.get("/discover", response_model=SuccessResponse[List[schemas.agent.Agent]])
def discover_agents(
    db: Session = Depends(get_db)
):
    """
    发现可用Agents
    """
    agents = crud.agent.get_available_agents(db)
    return SuccessResponse(
        data=[schemas.agent.Agent.from_orm(agent) for agent in agents]
    )

@router.post("/{agent_id}/load", response_model=SuccessResponse[schemas.agent.AgentLoadResponse])
def load_agent(
    agent_id: str,
    load_request: schemas.agent.AgentLoadRequest,
    db: Session = Depends(get_db)
):
    """
    加载Agent
    """
    # 检查Agent是否存在
    agent = crud.agent.get_by_id(db, agent_id=agent_id)
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent不存在"
        )
    
    # 创建Agent实例
    agent_instance = crud.agent.create_instance(
        db, 
        agent_id=agent_id, 
        config=load_request.config
    )
    
    return SuccessResponse(
        data=schemas.agent.AgentLoadResponse(
            agent_id=agent_id,
            status="loaded",
            instance_id=agent_instance.instance_id,
            loaded_at=agent_instance.created_at
        )
    )

@router.get("/loaded", response_model=SuccessResponse[List[schemas.agent.AgentInstance]])
def get_loaded_agents(
    db: Session = Depends(get_db)
):
    """
    获取已加载的Agents
    """
    instances = crud.agent.get_loaded_instances(db)
    return SuccessResponse(
        data=[schemas.agent.AgentInstance.from_orm(instance) for instance in instances]
    )

@router.get("/{agent_id}/status", response_model=SuccessResponse[schemas.agent.AgentInstance])
def get_agent_status(
    agent_id: str,
    db: Session = Depends(get_db)
):
    """
    获取Agent状态
    """
    instance = crud.agent.get_instance_by_agent_id(db, agent_id=agent_id)
    if not instance:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent实例不存在"
        )
    
    return SuccessResponse(
        data=schemas.agent.AgentInstance.from_orm(instance)
    )