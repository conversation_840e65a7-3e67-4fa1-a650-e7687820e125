# AgentsUI 数据库设计文档

## 1. 数据库概述

### 1.1 数据库选择
- **主数据库**: PostgreSQL 15+
- **缓存数据库**: Redis 7+
- **文件存储**: 本地文件系统 / MinIO / AWS S3

### 1.2 数据库连接配置
- **主机地址**: 127.0.0.1
- **端口**: 5432
- **用户名**: postgres
- **密码**: 1qaz@wsX
- **数据库名**: agentsui
- **连接字符串**: postgresql://postgres:1qaz@wsX@127.0.0.1:5432/agentsui

### 1.2 设计原则
- 数据一致性和完整性
- 高性能查询优化
- 可扩展性设计
- 数据安全和隐私保护

### 1.3 命名规范
- 表名：小写字母 + 下划线，复数形式（如：users, conversations）
- 字段名：小写字母 + 下划线（如：user_id, created_at）
- 索引名：idx_表名_字段名（如：idx_users_email）
- 外键名：fk_表名_字段名（如：fk_conversations_user_id）

## 2. 数据库架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        用户管理模块                          │
├─────────────────────────────────────────────────────────────┤
│  users  │  user_roles  │  permissions  │  role_permissions  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        Agent管理模块                        │
├─────────────────────────────────────────────────────────────┤
│  agents  │  agent_instances  │  agent_configs  │  user_agents│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        对话管理模块                          │
├─────────────────────────────────────────────────────────────┤
│  conversations  │  messages  │  message_attachments         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        知识库模块                            │
├─────────────────────────────────────────────────────────────┤
│  knowledge_bases  │  documents  │  document_chunks          │
│  agent_knowledge_bases  │  document_embeddings             │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        模型管理模块                          │
├─────────────────────────────────────────────────────────────┤
│  ai_models  │  model_configs  │  agent_models              │
│  model_usage_logs  │  model_api_keys                       │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        MCP集成模块                           │
├─────────────────────────────────────────────────────────────┤
│  mcp_servers  │  mcp_connections  │  mcp_resources          │
│  mcp_tools    │  mcp_prompts      │  user_mcp_servers       │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        系统管理模块                          │
├─────────────────────────────────────────────────────────────┤
│  system_configs  │  audit_logs  │  api_keys                │
└─────────────────────────────────────────────────────────────┘
```

## 3. 核心数据表设计

### 3.1 用户管理模块

#### users - 用户表
```sql
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### user_roles - 用户角色表
```sql
CREATE TABLE user_roles (
    role_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认角色
INSERT INTO user_roles (role_name, description) VALUES 
('admin', '系统管理员'),
('user', '普通用户'),
('guest', '访客用户');
```

#### user_role_assignments - 用户角色分配表
```sql
CREATE TABLE user_role_assignments (
    assignment_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES user_roles(role_id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(user_id),
    UNIQUE(user_id, role_id)
);
```

### 3.2 Agent管理模块

#### agents - Agent定义表
```sql
CREATE TABLE agents (
    agent_id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    version VARCHAR(20) NOT NULL,
    author VARCHAR(100),
    tags TEXT[], -- PostgreSQL数组类型
    config_schema JSONB, -- Agent配置模式
    file_path VARCHAR(500) NOT NULL, -- Agent文件路径
    entry_point VARCHAR(200) NOT NULL, -- 入口点
    requirements TEXT[], -- 依赖列表
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_agents_name ON agents(name);
CREATE INDEX idx_agents_tags ON agents USING GIN(tags);
```

#### agent_instances - Agent实例表
```sql
CREATE TABLE agent_instances (
    instance_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id VARCHAR(100) NOT NULL REFERENCES agents(agent_id),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    instance_name VARCHAR(200),
    config JSONB, -- Agent配置
    status VARCHAR(20) DEFAULT 'stopped', -- stopped, starting, running, error
    process_id INTEGER,
    port INTEGER,
    health_status VARCHAR(20) DEFAULT 'unknown',
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_agent_instances_user_id ON agent_instances(user_id);
CREATE INDEX idx_agent_instances_agent_id ON agent_instances(agent_id);
CREATE INDEX idx_agent_instances_status ON agent_instances(status);
```

#### user_agents - 用户Agent关联表
```sql
CREATE TABLE user_agents (
    user_agent_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    agent_id VARCHAR(100) NOT NULL REFERENCES agents(agent_id),
    is_favorite BOOLEAN DEFAULT false,
    access_level VARCHAR(20) DEFAULT 'read', -- read, write, admin
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, agent_id)
);
```

### 3.3 对话管理模块

#### conversations - 对话会话表
```sql
CREATE TABLE conversations (
    conversation_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    agent_id VARCHAR(100) NOT NULL REFERENCES agents(agent_id),
    instance_id UUID REFERENCES agent_instances(instance_id),
    title VARCHAR(500),
    status VARCHAR(20) DEFAULT 'active', -- active, archived, deleted
    context JSONB, -- 对话上下文
    metadata JSONB, -- 元数据
    message_count INTEGER DEFAULT 0,
    last_message_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_agent_id ON conversations(agent_id);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);
```

#### messages - 消息表
```sql
CREATE TABLE messages (
    message_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    sender VARCHAR(20) NOT NULL, -- user, agent, system
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text', -- text, image, file, json
    data JSONB, -- 结构化数据
    parent_message_id UUID REFERENCES messages(message_id),
    is_edited BOOLEAN DEFAULT false,
    edit_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender ON messages(sender);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_messages_parent_id ON messages(parent_message_id);
```

#### message_attachments - 消息附件表
```sql
CREATE TABLE message_attachments (
    attachment_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID NOT NULL REFERENCES messages(message_id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 3.4 知识库模块

#### knowledge_bases - 知识库表
```sql
CREATE TABLE knowledge_bases (
    kb_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    owner_id UUID NOT NULL REFERENCES users(user_id),
    is_public BOOLEAN DEFAULT false,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    chunk_size INTEGER DEFAULT 1000,
    chunk_overlap INTEGER DEFAULT 200,
    document_count INTEGER DEFAULT 0,
    total_size BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_knowledge_bases_owner_id ON knowledge_bases(owner_id);
CREATE INDEX idx_knowledge_bases_category ON knowledge_bases(category);
```

#### documents - 文档表
```sql
CREATE TABLE documents (
    document_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    kb_id UUID NOT NULL REFERENCES knowledge_bases(kb_id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    content TEXT,
    file_path VARCHAR(500),
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    tags TEXT[],
    metadata JSONB,
    status VARCHAR(20) DEFAULT 'processing', -- processing, ready, error
    chunk_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_documents_kb_id ON documents(kb_id);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_tags ON documents USING GIN(tags);
```

#### document_chunks - 文档块表
```sql
CREATE TABLE document_chunks (
    chunk_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(document_id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB,
    token_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_document_chunks_index ON document_chunks(document_id, chunk_index);
```

#### document_embeddings - 文档向量表
```sql
CREATE TABLE document_embeddings (
    embedding_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chunk_id UUID NOT NULL REFERENCES document_chunks(chunk_id) ON DELETE CASCADE,
    embedding VECTOR(1536), -- 使用pgvector扩展
    model VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 向量索引
CREATE INDEX idx_document_embeddings_vector ON document_embeddings 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
```

#### agent_knowledge_bases - Agent知识库关联表
```sql
CREATE TABLE agent_knowledge_bases (
    agent_kb_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id VARCHAR(100) NOT NULL REFERENCES agents(agent_id),
    kb_id UUID NOT NULL REFERENCES knowledge_bases(kb_id) ON DELETE CASCADE,
    access_level VARCHAR(20) DEFAULT 'read', -- read, write
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, kb_id)
);
```

### 3.5 模型管理模块

#### ai_models - AI模型表
```sql
CREATE TABLE ai_models (
    model_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    provider VARCHAR(50) NOT NULL, -- openai, deepseek, kimi, etc.
    model_type VARCHAR(50) DEFAULT 'chat', -- chat, completion, embedding
    description TEXT,
    api_endpoint VARCHAR(500),
    max_tokens INTEGER DEFAULT 4096,
    supports_streaming BOOLEAN DEFAULT true,
    supports_functions BOOLEAN DEFAULT false,
    cost_per_1k_input DECIMAL(10,6) DEFAULT 0,
    cost_per_1k_output DECIMAL(10,6) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_ai_models_provider ON ai_models(provider);
CREATE INDEX idx_ai_models_type ON ai_models(model_type);
CREATE INDEX idx_ai_models_active ON ai_models(is_active);

-- 插入预设模型
INSERT INTO ai_models (model_name, display_name, provider, description, api_endpoint) VALUES
('deepseek', 'DeepSeek Chat', 'deepseek', 'DeepSeek对话模型，支持代码生成和推理', 'https://api.deepseek.com/v1'),
('kimi-k2', 'Kimi K2', 'kimi', 'Kimi长文本模型，支持多模态交互', 'https://api.moonshot.cn/v1');
```

#### model_api_keys - 模型API密钥表
```sql
CREATE TABLE model_api_keys (
    key_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id UUID NOT NULL REFERENCES ai_models(model_id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    key_name VARCHAR(100) NOT NULL,
    api_key_encrypted TEXT NOT NULL, -- 加密存储的API密钥
    api_key_hash VARCHAR(255) NOT NULL, -- API密钥的哈希值，用于验证
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_model_api_keys_model_id ON model_api_keys(model_id);
CREATE INDEX idx_model_api_keys_user_id ON model_api_keys(user_id);
CREATE INDEX idx_model_api_keys_active ON model_api_keys(is_active);

-- 插入预设API密钥（注意：实际部署时应加密存储）
INSERT INTO model_api_keys (model_id, key_name, api_key_encrypted, api_key_hash) VALUES
((SELECT model_id FROM ai_models WHERE model_name = 'deepseek'), 'DeepSeek Default Key', 'encrypted_sk-2eace239cf454e75a8e16ed2e05405a7', 'hash_of_deepseek_key'),
((SELECT model_id FROM ai_models WHERE model_name = 'kimi-k2'), 'Kimi Default Key', 'encrypted_sk-7g3tSy9agawgEu7eHRHGjRsKMUCsgTq0oRKPOhgf2IcoDVex', 'hash_of_kimi_key');
```

#### model_configs - 模型配置表
```sql
CREATE TABLE model_configs (
    config_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id UUID NOT NULL REFERENCES ai_models(model_id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    config_name VARCHAR(100) NOT NULL,
    temperature DECIMAL(3,2) DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 1000,
    top_p DECIMAL(3,2) DEFAULT 1.0,
    frequency_penalty DECIMAL(3,2) DEFAULT 0.0,
    presence_penalty DECIMAL(3,2) DEFAULT 0.0,
    stop_sequences TEXT[], -- 停止序列
    custom_params JSONB, -- 自定义参数
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_model_configs_model_id ON model_configs(model_id);
CREATE INDEX idx_model_configs_user_id ON model_configs(user_id);
```

#### agent_models - Agent模型关联表
```sql
CREATE TABLE agent_models (
    agent_model_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id VARCHAR(100) NOT NULL REFERENCES agents(agent_id),
    model_id UUID NOT NULL REFERENCES ai_models(model_id),
    config_id UUID REFERENCES model_configs(config_id),
    is_primary BOOLEAN DEFAULT true, -- 是否为主要模型
    priority INTEGER DEFAULT 1, -- 优先级，数字越小优先级越高
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, model_id)
);

-- 索引
CREATE INDEX idx_agent_models_agent_id ON agent_models(agent_id);
CREATE INDEX idx_agent_models_model_id ON agent_models(model_id);
CREATE INDEX idx_agent_models_primary ON agent_models(is_primary);
```

#### model_usage_logs - 模型使用日志表
```sql
CREATE TABLE model_usage_logs (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id UUID NOT NULL REFERENCES ai_models(model_id),
    agent_id VARCHAR(100) REFERENCES agents(agent_id),
    user_id UUID REFERENCES users(user_id),
    conversation_id UUID REFERENCES conversations(conversation_id),
    request_tokens INTEGER DEFAULT 0,
    response_tokens INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    response_time_ms INTEGER, -- 响应时间（毫秒）
    status VARCHAR(20) DEFAULT 'success', -- success, error, timeout
    error_message TEXT,
    cost_amount DECIMAL(10,6) DEFAULT 0, -- 成本金额
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_model_usage_logs_model_id ON model_usage_logs(model_id);
CREATE INDEX idx_model_usage_logs_agent_id ON model_usage_logs(agent_id);
CREATE INDEX idx_model_usage_logs_user_id ON model_usage_logs(user_id);
CREATE INDEX idx_model_usage_logs_created_at ON model_usage_logs(created_at);
CREATE INDEX idx_model_usage_logs_status ON model_usage_logs(status);
```

### 3.6 MCP集成模块

#### mcp_servers - MCP服务器表
```sql
CREATE TABLE mcp_servers (
    server_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    server_type VARCHAR(50) DEFAULT 'external', -- external, local, docker
    connection_config JSONB NOT NULL, -- 连接配置
    capabilities JSONB, -- 服务器能力
    version VARCHAR(20),
    author VARCHAR(100),
    tags TEXT[],
    is_active BOOLEAN DEFAULT true,
    is_public BOOLEAN DEFAULT false, -- 是否为公共服务器
    health_status VARCHAR(20) DEFAULT 'unknown', -- healthy, unhealthy, unknown
    last_health_check TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_mcp_servers_name ON mcp_servers(name);
CREATE INDEX idx_mcp_servers_type ON mcp_servers(server_type);
CREATE INDEX idx_mcp_servers_tags ON mcp_servers USING GIN(tags);
```

#### mcp_connections - MCP连接表
```sql
CREATE TABLE mcp_connections (
    connection_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    server_id UUID NOT NULL REFERENCES mcp_servers(server_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    connection_name VARCHAR(200),
    config JSONB, -- 用户特定配置
    status VARCHAR(20) DEFAULT 'disconnected', -- connected, disconnected, error
    last_connected_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(server_id, user_id)
);

-- 索引
CREATE INDEX idx_mcp_connections_server_id ON mcp_connections(server_id);
CREATE INDEX idx_mcp_connections_user_id ON mcp_connections(user_id);
CREATE INDEX idx_mcp_connections_status ON mcp_connections(status);
```

#### mcp_resources - MCP资源表
```sql
CREATE TABLE mcp_resources (
    resource_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    server_id UUID NOT NULL REFERENCES mcp_servers(server_id) ON DELETE CASCADE,
    resource_uri VARCHAR(500) NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    mime_type VARCHAR(100),
    metadata JSONB,
    is_available BOOLEAN DEFAULT true,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(server_id, resource_uri)
);

-- 索引
CREATE INDEX idx_mcp_resources_server_id ON mcp_resources(server_id);
CREATE INDEX idx_mcp_resources_uri ON mcp_resources(resource_uri);
```

#### mcp_tools - MCP工具表
```sql
CREATE TABLE mcp_tools (
    tool_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    server_id UUID NOT NULL REFERENCES mcp_servers(server_id) ON DELETE CASCADE,
    tool_name VARCHAR(200) NOT NULL,
    description TEXT,
    input_schema JSONB, -- 输入参数模式
    is_available BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    last_used TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(server_id, tool_name)
);

-- 索引
CREATE INDEX idx_mcp_tools_server_id ON mcp_tools(server_id);
CREATE INDEX idx_mcp_tools_name ON mcp_tools(tool_name);
```

#### mcp_prompts - MCP提示词表
```sql
CREATE TABLE mcp_prompts (
    prompt_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    server_id UUID NOT NULL REFERENCES mcp_servers(server_id) ON DELETE CASCADE,
    prompt_name VARCHAR(200) NOT NULL,
    description TEXT,
    template TEXT NOT NULL,
    arguments JSONB, -- 参数定义
    is_available BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    last_used TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(server_id, prompt_name)
);

-- 索引
CREATE INDEX idx_mcp_prompts_server_id ON mcp_prompts(server_id);
CREATE INDEX idx_mcp_prompts_name ON mcp_prompts(prompt_name);
```

#### user_mcp_servers - 用户MCP服务器关联表
```sql
CREATE TABLE user_mcp_servers (
    user_server_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    server_id UUID NOT NULL REFERENCES mcp_servers(server_id) ON DELETE CASCADE,
    access_level VARCHAR(20) DEFAULT 'read', -- read, write, admin
    is_favorite BOOLEAN DEFAULT false,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, server_id)
);
```

### 3.6 系统管理模块

#### system_configs - 系统配置表
```sql
CREATE TABLE system_configs (
    config_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    is_encrypted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### audit_logs - 审计日志表
```sql
CREATE TABLE audit_logs (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

#### api_keys - API密钥表
```sql
CREATE TABLE api_keys (
    key_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    key_name VARCHAR(100) NOT NULL,
    key_hash VARCHAR(255) NOT NULL,
    key_prefix VARCHAR(20) NOT NULL,
    permissions TEXT[],
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## 4. 数据关系图

```mermaid
erDiagram
    users ||--o{ user_role_assignments : has
    user_roles ||--o{ user_role_assignments : assigned
    users ||--o{ agent_instances : owns
    users ||--o{ conversations : creates
    users ||--o{ knowledge_bases : owns
    
    agents ||--o{ agent_instances : instantiated
    agents ||--o{ conversations : used_in
    agents ||--o{ agent_knowledge_bases : accesses
    
    agent_instances ||--o{ conversations : handles
    conversations ||--o{ messages : contains
    messages ||--o{ message_attachments : has
    
    knowledge_bases ||--o{ documents : contains
    knowledge_bases ||--o{ agent_knowledge_bases : accessed_by
    documents ||--o{ document_chunks : split_into
    document_chunks ||--o{ document_embeddings : vectorized
```

## 5. 数据库优化

### 5.1 索引策略
```sql
-- 复合索引
CREATE INDEX idx_conversations_user_agent ON conversations(user_id, agent_id);
CREATE INDEX idx_messages_conv_created ON messages(conversation_id, created_at);

-- 部分索引
CREATE INDEX idx_active_agent_instances ON agent_instances(user_id) 
WHERE status = 'running';

-- 表达式索引
CREATE INDEX idx_users_email_lower ON users(LOWER(email));
```

### 5.2 分区策略
```sql
-- 按时间分区消息表
CREATE TABLE messages_2024_01 PARTITION OF messages
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 按哈希分区审计日志
CREATE TABLE audit_logs_0 PARTITION OF audit_logs
FOR VALUES WITH (MODULUS 4, REMAINDER 0);
```

### 5.3 性能优化
```sql
-- 更新统计信息
ANALYZE;

-- 查询计划分析
EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM conversations WHERE user_id = $1;

-- 连接池配置
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
```

## 6. 数据备份和恢复

### 6.1 备份策略
```bash
# 全量备份
pg_dump -h localhost -U postgres -d agentsui > backup_$(date +%Y%m%d).sql

# 增量备份（WAL归档）
archive_mode = on
archive_command = 'cp %p /backup/wal/%f'
```

### 6.2 恢复策略
```bash
# 从备份恢复
psql -h localhost -U postgres -d agentsui < backup_20240101.sql

# 时间点恢复
pg_basebackup -h localhost -D /backup/base -U postgres
```

## 7. 数据迁移

### 7.1 Alembic配置
```python
# alembic/env.py
from sqlalchemy import engine_from_config
from app.models import Base

target_metadata = Base.metadata
```

### 7.2 迁移脚本示例
```python
# migrations/versions/001_initial.py
def upgrade():
    op.create_table('users',
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('username', sa.String(50), nullable=False),
        sa.Column('email', sa.String(255), nullable=False),
        sa.PrimaryKeyConstraint('user_id')
    )

def downgrade():
    op.drop_table('users')
```
