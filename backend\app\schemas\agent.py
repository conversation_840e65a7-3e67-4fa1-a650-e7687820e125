from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from uuid import UUID

class AgentBase(BaseModel):
    name: str = Field(..., max_length=200)
    description: Optional[str] = None
    version: str = Field(..., max_length=20)
    author: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None

class AgentCreate(AgentBase):
    agent_id: str = Field(..., max_length=100)
    file_path: str = Field(..., max_length=500)
    entry_point: str = Field(..., max_length=200)
    requirements: Optional[List[str]] = None
    config_schema: Optional[Dict[str, Any]] = None

class AgentUpdate(AgentBase):
    pass

class Agent(AgentBase):
    agent_id: str
    config_schema: Optional[Dict[str, Any]] = None
    file_path: str
    entry_point: str
    requirements: Optional[List[str]] = None
    is_available: bool = True
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class AgentInstanceBase(BaseModel):
    agent_id: str = Field(..., max_length=100)
    instance_name: Optional[str] = Field(None, max_length=200)
    config: Optional[Dict[str, Any]] = None

class AgentInstanceCreate(AgentInstanceBase):
    pass

class AgentInstance(AgentInstanceBase):
    instance_id: UUID
    user_id: UUID
    status: str = "stopped"
    process_id: Optional[int] = None
    port: Optional[int] = None
    health_status: str = "unknown"
    last_heartbeat: Optional[datetime] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class AgentLoadRequest(BaseModel):
    config: Optional[Dict[str, Any]] = None

class AgentLoadResponse(BaseModel):
    agent_id: str
    status: str
    instance_id: UUID
    loaded_at: datetime