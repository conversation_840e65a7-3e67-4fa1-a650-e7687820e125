# AgentsUI 技术架构设计文档

## 1. 项目概述

AgentsUI 是一个基于 Web 的多 Agent 管理和交互平台，支持 LangChain、LangGraph 开发的 Agent 集成，提供统一的用户界面和管理功能。

### 1.1 核心功能
- Agent 管理：加载、配置、启动、停止 Agent
- 实时对话：通过 WebSocket 与 Agent 进行实时交互
- 知识库管理：管理 Agent 使用的知识库和文档
- MCP 集成：支持 Model Context Protocol，集成第三方 MCP 服务
- 用户管理：多用户支持，权限控制
- 主题切换：支持浅色/深色模式

### 1.2 技术目标
- 高性能：支持多个 Agent 并发运行
- 可扩展：易于集成新的 Agent
- 用户友好：直观的 Web 界面
- 实时性：低延迟的对话体验

## 2. 整体架构

### 2.1 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  React 18 + TypeScript + Tailwind CSS + Zustand            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   导航组件   │ │   Agent列表  │ │   对话组件   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   知识库     │ │   设置面板   │ │   工具面板   │           │
│  │             │ │  (含MCP配置) │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTP/WebSocket
                              │
┌─────────────────────────────────────────────────────────────┐
│                        API网关层                             │
├─────────────────────────────────────────────────────────────┤
│  FastAPI + Uvicorn                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  认证中间件  │ │  CORS中间件  │ │  日志中间件  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        业务逻辑层                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Agent管理器  │ │ 对话管理器   │ │ 知识库管理   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 用户管理     │ │ WebSocket   │ │ MCP管理器    │           │
│  │             │ │ 连接管理     │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 模型管理器   │ │ API密钥管理  │ │ 使用统计     │           │
│  │             │ │             │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        Agent运行层                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Agent进程池  │ │ LangGraph   │ │ AG-UI协议   │           │
│  │             │ │ 状态机       │ │ 适配器       │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ MCP客户端    │ │ MCP服务器   │ │ JSON-RPC    │           │
│  │ 连接池       │ │ 代理         │ │ 通信层       │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ PostgreSQL  │ │    Redis    │ │ 文件存储     │           │
│  │ (主数据库)   │ │ (缓存/会话)  │ │ (知识库)     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 技术栈选择

#### 前端技术栈
- **React 18**: 现代化的前端框架，支持并发特性
- **TypeScript**: 类型安全，提高代码质量
- **Tailwind CSS**: 实用优先的CSS框架，快速构建UI
- **Zustand**: 轻量级状态管理库
- **React Router**: 客户端路由
- **Socket.IO Client**: WebSocket客户端
- **Axios**: HTTP客户端
- **React Query**: 服务端状态管理

#### 后端技术栈
- **FastAPI**: 高性能的Python Web框架
- **Uvicorn**: ASGI服务器
- **SQLAlchemy**: ORM框架
- **Alembic**: 数据库迁移工具
- **Pydantic**: 数据验证和序列化
- **Socket.IO**: WebSocket服务端
- **Celery**: 异步任务队列
- **Redis**: 缓存和消息队列
- **PostgreSQL**: 主数据库

#### Agent技术栈
- **LangChain**: Agent开发框架
- **LangGraph**: 状态机和工作流
- **OpenAI API**: 大语言模型
- **AG-UI协议**: Agent与UI的通信协议
- **MCP协议**: Model Context Protocol，标准化上下文集成

## 3. 核心模块设计

### 3.1 Agent管理模块

#### 功能职责
- Agent发现：扫描agents/目录下的Agent
- Agent加载：动态加载Agent模块
- Agent生命周期：启动、停止、重启Agent
- Agent配置：管理Agent的配置参数
- Agent监控：监控Agent运行状态

#### 核心类设计
```python
class AgentManager:
    def discover_agents(self) -> List[AgentInfo]
    def load_agent(self, agent_id: str) -> Agent
    def start_agent(self, agent_id: str) -> bool
    def stop_agent(self, agent_id: str) -> bool
    def get_agent_status(self, agent_id: str) -> AgentStatus
    def configure_agent(self, agent_id: str, config: dict) -> bool
```

### 3.2 WebSocket通信模块

#### AG-UI协议规范
```json
{
  "type": "message|question|confirmation|symbols|error",
  "content": "消息内容",
  "data": {
    "step": "当前步骤",
    "waiting_for_user": true,
    "input_type": "text|confirmation|answers"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### WebSocket连接管理
```python
class WebSocketManager:
    def connect(self, websocket: WebSocket, user_id: str, agent_id: str)
    def disconnect(self, websocket: WebSocket)
    def send_message(self, user_id: str, agent_id: str, message: dict)
    def broadcast(self, message: dict)
    def get_active_connections(self) -> List[WebSocket]
```

### 3.3 对话管理模块

#### 对话会话管理
```python
class ConversationManager:
    def create_session(self, user_id: str, agent_id: str) -> str
    def get_session(self, session_id: str) -> ConversationSession
    def save_message(self, session_id: str, message: Message)
    def get_history(self, session_id: str) -> List[Message]
    def end_session(self, session_id: str)
```

### 3.4 模型管理模块

#### 模型配置管理
```python
class ModelManager:
    def discover_models(self) -> List[ModelInfo]
    def register_model(self, config: ModelConfig) -> str
    def update_model(self, model_id: str, updates: dict) -> bool
    def delete_model(self, model_id: str) -> bool
    def test_model_connection(self, model_id: str) -> bool
    def get_model_client(self, model_id: str) -> ModelClient
```

#### API密钥管理
```python
class APIKeyManager:
    def add_api_key(self, model_id: str, api_key: str) -> str
    def encrypt_api_key(self, api_key: str) -> str
    def decrypt_api_key(self, encrypted_key: str) -> str
    def validate_api_key(self, model_id: str, api_key: str) -> bool
    def rotate_api_key(self, model_id: str) -> str
```

#### 使用统计管理
```python
class UsageTracker:
    def record_usage(self, model_id: str, usage_data: dict) -> None
    def get_usage_stats(self, model_id: str, period: str) -> dict
    def get_cost_analysis(self, user_id: str, period: str) -> dict
    def check_quota_limits(self, user_id: str) -> bool
    def generate_usage_report(self, period: str) -> dict
```

### 3.5 MCP管理模块

#### MCP服务管理
```python
class MCPManager:
    def discover_mcp_servers(self) -> List[MCPServerInfo]
    def register_mcp_server(self, config: MCPServerConfig) -> str
    def connect_to_server(self, server_id: str) -> MCPClient
    def disconnect_from_server(self, server_id: str) -> bool
    def get_server_capabilities(self, server_id: str) -> MCPCapabilities
    def list_resources(self, server_id: str) -> List[MCPResource]
    def list_tools(self, server_id: str) -> List[MCPTool]
    def list_prompts(self, server_id: str) -> List[MCPPrompt]
    def invoke_tool(self, server_id: str, tool_name: str, params: dict) -> Any
    def get_resource(self, server_id: str, resource_uri: str) -> MCPResource
```

#### MCP客户端连接池
```python
class MCPClientPool:
    def get_client(self, server_id: str) -> MCPClient
    def create_client(self, config: MCPServerConfig) -> MCPClient
    def remove_client(self, server_id: str) -> bool
    def health_check(self, server_id: str) -> bool
    def reconnect(self, server_id: str) -> bool
```

## 4. 数据库设计概览

### 4.1 核心数据表
- **users**: 用户信息
- **agents**: Agent配置信息
- **conversations**: 对话会话
- **messages**: 对话消息
- **knowledge_bases**: 知识库
- **documents**: 文档管理
- **user_agents**: 用户Agent关联

### 4.2 数据关系
```
users (1) ←→ (N) user_agents (N) ←→ (1) agents
users (1) ←→ (N) conversations (N) ←→ (1) agents
conversations (1) ←→ (N) messages
knowledge_bases (1) ←→ (N) documents
agents (N) ←→ (N) knowledge_bases
```

## 5. 安全设计

### 5.1 认证授权
- JWT Token认证
- 基于角色的访问控制(RBAC)
- API密钥管理
- 会话管理

### 5.2 数据安全
- 数据库连接加密
- 敏感数据加密存储
- API接口限流
- 输入验证和过滤

## 6. 性能优化

### 6.1 前端优化
- 代码分割和懒加载
- 组件缓存
- 虚拟滚动
- 图片懒加载

### 6.2 后端优化
- 数据库连接池
- Redis缓存
- 异步处理
- 负载均衡

## 7. 部署架构

### 7.1 容器化部署
```yaml
services:
  frontend:
    image: agentsui-frontend:latest
    ports:
      - "3000:3000"
  
  backend:
    image: agentsui-backend:latest
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
  
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: agentsui
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1qaz@wsX
  
  redis:
    image: redis:7-alpine
```

### 7.2 生产环境
- Nginx反向代理
- Docker Swarm集群
- 数据库主从复制
- 监控告警系统

## 8. 开发规范

### 8.1 代码规范
- Python: PEP 8 + Black格式化
- TypeScript: ESLint + Prettier
- Git提交规范: Conventional Commits

### 8.2 测试策略
- 单元测试覆盖率 > 80%
- 集成测试
- E2E测试
- 性能测试

## 9. 后续扩展

### 9.1 功能扩展
- 多语言支持
- 插件系统
- 工作流编排
- 数据分析面板

### 9.2 技术扩展
- 微服务架构
- 容器编排(Kubernetes)
- 服务网格(Istio)
- 云原生部署
