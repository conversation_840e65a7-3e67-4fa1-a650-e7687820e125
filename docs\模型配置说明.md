# AgentsUI 模型配置说明

## 1. 预设模型配置

### 1.1 DeepSeek模型
```json
{
  "model_name": "deepseek",
  "display_name": "DeepSeek Chat",
  "provider": "deepseek",
  "model_type": "chat",
  "description": "DeepSeek对话模型，支持代码生成和推理",
  "api_endpoint": "https://api.deepseek.com/v1",
  "api_key": "***********************************",
  "max_tokens": 4096,
  "supports_streaming": true,
  "supports_functions": false,
  "cost_per_1k_input": 0.001,
  "cost_per_1k_output": 0.002,
  "default_config": {
    "temperature": 0.7,
    "max_tokens": 1000,
    "top_p": 1.0,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0
  }
}
```

### 1.2 Kimi模型
```json
{
  "model_name": "kimi-k2",
  "display_name": "<PERSON><PERSON> K2",
  "provider": "kimi",
  "model_type": "chat",
  "description": "Kimi长文本模型，支持多模态交互",
  "api_endpoint": "https://api.moonshot.cn/v1",
  "api_key": "sk-7g3tSy9agawgEu7eHRHGjRsKMUCsgTq0oRKPOhgf2IcoDVex",
  "max_tokens": 128000,
  "supports_streaming": true,
  "supports_functions": true,
  "cost_per_1k_input": 0.0015,
  "cost_per_1k_output": 0.003,
  "default_config": {
    "temperature": 0.7,
    "max_tokens": 2000,
    "top_p": 0.95,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0
  }
}
```

## 2. 环境变量配置

### 2.1 开发环境配置
```bash
# .env文件
# AI模型API密钥
DEEPSEEK_API_KEY=***********************************
KIMI_API_KEY=sk-7g3tSy9agawgEu7eHRHGjRsKMUCsgTq0oRKPOhgf2IcoDVex
OPENAI_API_KEY=sk-your-openai-api-key

# 模型配置
DEFAULT_MODEL=deepseek
MODEL_FALLBACK_ENABLED=true
MODEL_TIMEOUT=30
MODEL_RETRY_COUNT=3

# 模型成本控制
MAX_DAILY_COST_PER_USER=10.00
COST_ALERT_THRESHOLD=8.00
```

### 2.2 生产环境配置
```bash
# 生产环境应使用更安全的密钥管理
DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
KIMI_API_KEY=${KIMI_API_KEY}
OPENAI_API_KEY=${OPENAI_API_KEY}

# 生产环境模型配置
DEFAULT_MODEL=deepseek
MODEL_FALLBACK_ENABLED=true
MODEL_LOAD_BALANCING=true
MODEL_RATE_LIMITING=true
```

## 3. 数据库初始化脚本

### 3.1 模型数据初始化
```sql
-- 插入预设模型
INSERT INTO ai_models (model_name, display_name, provider, model_type, description, api_endpoint, max_tokens, supports_streaming, supports_functions, cost_per_1k_input, cost_per_1k_output, is_active, is_default) VALUES 
('deepseek', 'DeepSeek Chat', 'deepseek', 'chat', 'DeepSeek对话模型，支持代码生成和推理', 'https://api.deepseek.com/v1', 4096, true, false, 0.001, 0.002, true, true),
('kimi-k2', 'Kimi K2', 'kimi', 'chat', 'Kimi长文本模型，支持多模态交互', 'https://api.moonshot.cn/v1', 128000, true, true, 0.0015, 0.003, true, false);

-- 插入API密钥（注意：实际部署时应加密存储）
INSERT INTO model_api_keys (model_id, key_name, api_key_encrypted, api_key_hash, is_active) VALUES 
((SELECT model_id FROM ai_models WHERE model_name = 'deepseek'), 'DeepSeek Default Key', 'encrypted_***********************************', 'hash_deepseek', true),
((SELECT model_id FROM ai_models WHERE model_name = 'kimi-k2'), 'Kimi Default Key', 'encrypted_sk-7g3tSy9agawgEu7eHRHGjRsKMUCsgTq0oRKPOhgf2IcoDVex', 'hash_kimi', true);

-- 插入默认配置
INSERT INTO model_configs (model_id, config_name, temperature, max_tokens, top_p, frequency_penalty, presence_penalty, is_default) VALUES 
((SELECT model_id FROM ai_models WHERE model_name = 'deepseek'), 'Default Config', 0.7, 1000, 1.0, 0.0, 0.0, true),
((SELECT model_id FROM ai_models WHERE model_name = 'kimi-k2'), 'Default Config', 0.7, 2000, 0.95, 0.0, 0.0, true);
```

## 4. Agent模型绑定配置

### 4.1 默认模型绑定
```python
# agents/culture_agent/config.json
{
  "agent_id": "culture_agent",
  "name": "企业文化精神符号总结Agent",
  "model_bindings": [
    {
      "model_name": "deepseek",
      "is_primary": true,
      "priority": 1,
      "config_overrides": {
        "temperature": 0.8,
        "max_tokens": 1500
      }
    },
    {
      "model_name": "kimi-k2",
      "is_primary": false,
      "priority": 2,
      "config_overrides": {
        "temperature": 0.7,
        "max_tokens": 2000
      }
    }
  ]
}
```

### 4.2 运行时模型切换
```python
# Agent中的模型切换示例
class CultureSymbolAgent(BaseAgent):
    async def switch_to_fallback_model(self):
        """切换到备用模型"""
        try:
            # 获取备用模型
            fallback_models = await self.get_fallback_models()
            if fallback_models:
                await self.switch_model(fallback_models[0].model_id)
                return True
        except Exception as e:
            self.logger.error(f"切换备用模型失败: {e}")
        return False
    
    async def handle_model_error(self, error):
        """处理模型错误"""
        if "rate_limit" in str(error).lower():
            # 速率限制，切换到备用模型
            await self.switch_to_fallback_model()
        elif "api_key" in str(error).lower():
            # API密钥问题，记录错误
            self.logger.error(f"API密钥错误: {error}")
        else:
            # 其他错误，重试
            await asyncio.sleep(1)
```

## 5. 模型客户端实现

### 5.1 统一模型客户端
```python
# backend/core/model_client.py
import asyncio
import aiohttp
from typing import Dict, Any, Optional, AsyncGenerator
from cryptography.fernet import Fernet

class ModelClient:
    """统一的模型客户端"""
    
    def __init__(self, model_config: Dict[str, Any], api_key: str):
        self.model_config = model_config
        self.api_key = api_key
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        config: Optional[Dict[str, Any]] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """聊天完成接口"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model_config["model_name"],
            "messages": messages,
            "temperature": config.get("temperature", 0.7),
            "max_tokens": config.get("max_tokens", 1000),
            "top_p": config.get("top_p", 1.0),
            "frequency_penalty": config.get("frequency_penalty", 0.0),
            "presence_penalty": config.get("presence_penalty", 0.0),
            "stream": stream
        }
        
        if stream:
            return self._stream_completion(headers, payload)
        else:
            return await self._single_completion(headers, payload)
    
    async def _single_completion(self, headers: Dict, payload: Dict) -> Dict[str, Any]:
        """单次完成请求"""
        async with self.session.post(
            f"{self.model_config['api_endpoint']}/chat/completions",
            headers=headers,
            json=payload
        ) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_text = await response.text()
                raise Exception(f"模型API调用失败: {response.status} - {error_text}")
    
    async def _stream_completion(self, headers: Dict, payload: Dict) -> AsyncGenerator[Dict[str, Any], None]:
        """流式完成请求"""
        async with self.session.post(
            f"{self.model_config['api_endpoint']}/chat/completions",
            headers=headers,
            json=payload
        ) as response:
            if response.status == 200:
                async for line in response.content:
                    if line:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            data_str = line_str[6:]
                            if data_str != '[DONE]':
                                try:
                                    data = json.loads(data_str)
                                    yield data
                                except json.JSONDecodeError:
                                    continue
            else:
                error_text = await response.text()
                raise Exception(f"流式API调用失败: {response.status} - {error_text}")
```

### 5.2 模型管理器
```python
# backend/core/model_manager.py
class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        self.models = {}
        self.api_keys = {}
        self.configs = {}
        self.usage_tracker = UsageTracker()
    
    async def initialize(self):
        """初始化模型管理器"""
        await self.load_models()
        await self.load_api_keys()
        await self.load_configs()
    
    async def get_model_client(self, model_id: str, config_id: Optional[str] = None) -> ModelClient:
        """获取模型客户端"""
        model = self.models.get(model_id)
        if not model:
            raise ValueError(f"模型 {model_id} 不存在")
        
        api_key = await self.get_decrypted_api_key(model_id)
        if not api_key:
            raise ValueError(f"模型 {model_id} 没有可用的API密钥")
        
        config = self.configs.get(config_id) if config_id else model.get("default_config", {})
        
        return ModelClient(model, api_key)
    
    async def get_decrypted_api_key(self, model_id: str) -> Optional[str]:
        """获取解密的API密钥"""
        encrypted_key = self.api_keys.get(model_id)
        if encrypted_key:
            # 解密API密钥
            fernet = Fernet(settings.ENCRYPTION_KEY)
            return fernet.decrypt(encrypted_key.encode()).decode()
        return None
    
    async def track_usage(self, model_id: str, usage_data: Dict[str, Any]):
        """跟踪模型使用情况"""
        await self.usage_tracker.record_usage(model_id, usage_data)
```

## 6. 安全配置

### 6.1 API密钥加密
```python
# backend/core/encryption.py
from cryptography.fernet import Fernet
import os

class APIKeyEncryption:
    """API密钥加密工具"""
    
    def __init__(self):
        self.key = os.getenv('ENCRYPTION_KEY', Fernet.generate_key())
        self.fernet = Fernet(self.key)
    
    def encrypt_api_key(self, api_key: str) -> str:
        """加密API密钥"""
        return self.fernet.encrypt(api_key.encode()).decode()
    
    def decrypt_api_key(self, encrypted_key: str) -> str:
        """解密API密钥"""
        return self.fernet.decrypt(encrypted_key.encode()).decode()
    
    def hash_api_key(self, api_key: str) -> str:
        """生成API密钥哈希"""
        import hashlib
        return hashlib.sha256(api_key.encode()).hexdigest()
```

### 6.2 访问控制
```python
# backend/api/models.py
from fastapi import Depends, HTTPException
from core.auth import get_current_user, require_permission

@router.post("/models/{model_id}/api-keys")
async def add_api_key(
    model_id: str,
    key_data: CreateApiKeyRequest,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permission("model:manage"))
):
    """添加API密钥（需要管理权限）"""
    pass

@router.get("/models/{model_id}/usage")
async def get_model_usage(
    model_id: str,
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permission("model:view_usage"))
):
    """查看模型使用统计（需要查看权限）"""
    pass
```

## 7. 监控和告警

### 7.1 使用监控
```python
# backend/core/monitoring.py
class ModelMonitoring:
    """模型监控"""
    
    async def check_model_health(self, model_id: str) -> Dict[str, Any]:
        """检查模型健康状态"""
        try:
            client = await self.model_manager.get_model_client(model_id)
            test_response = await client.chat_completion([
                {"role": "user", "content": "Hello"}
            ])
            
            return {
                "status": "healthy",
                "response_time": test_response.get("response_time"),
                "last_check": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "last_check": datetime.utcnow().isoformat()
            }
    
    async def check_cost_limits(self, user_id: str) -> Dict[str, Any]:
        """检查成本限制"""
        daily_cost = await self.usage_tracker.get_daily_cost(user_id)
        limit = settings.MAX_DAILY_COST_PER_USER
        
        return {
            "daily_cost": daily_cost,
            "limit": limit,
            "percentage": (daily_cost / limit) * 100,
            "exceeded": daily_cost > limit
        }
```

## 8. 故障排除

### 8.1 常见问题
1. **API密钥无效**
   - 检查密钥是否正确
   - 验证密钥是否过期
   - 确认密钥权限

2. **模型响应超时**
   - 检查网络连接
   - 调整超时设置
   - 切换到备用模型

3. **成本超限**
   - 检查使用统计
   - 调整成本限制
   - 优化模型使用

### 8.2 调试工具
```bash
# 测试模型连接
curl -X POST "http://localhost:8000/api/v1/models/{model_id}/test" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"test_message": "Hello"}'

# 查看模型使用统计
curl "http://localhost:8000/api/v1/models/{model_id}/usage?period=day" \
  -H "Authorization: Bearer <token>"
```
