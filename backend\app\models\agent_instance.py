from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.dialects.sqlite import VARCHAR
from app.db.base import Base
import uuid
from datetime import datetime

class AgentInstance(Base):
    __tablename__ = "agent_instances"
    
    # 使用VARCHAR代替UUID以兼容SQLite
    instance_id = Column(VARCHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String(100), Foreign<PERSON>ey("agents.agent_id"), nullable=False)
    user_id = Column(VARCHAR(36), ForeignKey("users.user_id"), nullable=False)
    instance_name = Column(String(200))
    # SQLite不支持JSON类型，存储为字符串
    config = Column(Text)  # 存储为JSON字符串
    status = Column(String(20), default="stopped")  # stopped, starting, running, error
    process_id = Column(Integer)
    port = Column(Integer)
    health_status = Column(String(20), default="unknown")
    last_heartbeat = Column(DateTime)
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)