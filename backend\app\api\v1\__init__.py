from fastapi import APIRouter
from app.api.v1 import users, agents, conversations, models

api_router = APIRouter()

api_router.include_router(users.router, prefix="/auth", tags=["auth"])
api_router.include_router(agents.router, prefix="/agents", tags=["agents"])
api_router.include_router(conversations.router, prefix="/conversations", tags=["conversations"])
api_router.include_router(models.router, prefix="/models", tags=["models"])

@api_router.get("/health")
def health_check():
    return {"status": "healthy"}